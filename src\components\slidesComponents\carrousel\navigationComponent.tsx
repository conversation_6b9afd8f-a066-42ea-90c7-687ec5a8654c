"use client";
import { useState } from "react";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa";

export const NavigationComponents = ({
  components,
  activeComponentIndex,
  setActiveComponentIndex,
  setAnimationKeyDelivery,
  setAnimationKeyProduction,
}: {
  components: JSX.Element[];
  activeComponentIndex: number;
  setActiveComponentIndex: React.Dispatch<React.SetStateAction<number>>;
  setAnimationKeyDelivery: React.Dispatch<React.SetStateAction<number>>;
  setAnimationKeyProduction: React.Dispatch<React.SetStateAction<number>>;
}) => {
  const [isGrabbing, setIsGrabbing] = useState<boolean>(false);

  return (
    <div
      className={`absolute top-[50%]  flex justify-between w-full items-center ${isGrabbing ? "cursor-grabbing" : "cursor-grab"}`}
      onMouseDown={() => setIsGrabbing(true)}
      onMouseUp={() => setIsGrabbing(false)}
      onMouseLeave={() => setIsGrabbing(false)}
      tabIndex={-1}
    >
      <button
        disabled={activeComponentIndex === 0}
        className={`disabled:opacity-0 w-10 h-10 flex justify-start  text-textColorPrimary ml-4`}
        onClick={() => {
          setActiveComponentIndex(activeComponentIndex - 1);
          setAnimationKeyProduction(Math.random());
        }}
      >
        <FaArrowLeft className="text-textColorPrimary arrowAnimatedLeft" />
      </button>
      <button
        disabled={activeComponentIndex === components.length - 1}
        className={`disabled:opacity-0 w-10 h-10 flex justify-end text-textColorPrimary mr-4`}
        onClick={() => {
          setActiveComponentIndex(activeComponentIndex + 1);
          setAnimationKeyDelivery(Math.random());
        }}
      >
        <FaArrowRight className="text-textColorPrimary arrowAnimatedRight" />
      </button>
    </div>
  );
};
