import { ICoordinates } from "../proposal";

export interface DeliveryType {
  situation: number;
  deliveryForecastDate?: string;
  collectionDate: string;
  deliveryDate?: string;
  events: EventType[];
}

export interface EventType {
  description: string;
  eventDate: string;
  city?: string | null;
  uf?: string | null;
}

export interface DeliveryInterface {
  localizacao: ICoordinates[];
  delivery: DeliveryType;
  deliveryByPartner: {
    isPartner: boolean;
    isDoubleDeliverySsw: boolean;
    isDoubleDeliveryFretefy: boolean;
  };
  order: string;
}
