"use client";

import { motion } from "framer-motion";
import React, { useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { Toaster } from "react-hot-toast";

interface IModalProps {
  isOpen: boolean;
  onRequestClose: () => void;
  shouldCloseOnOverlayClick?: boolean;
  className?: string;
  overlayClassName?: string;
  children: React.ReactNode;
}

export const Modal = ({
  isOpen,
  onRequestClose,
  shouldCloseOnOverlayClick,
  className,
  overlayClassName,
  children,
}: IModalProps) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const previusActiveElement = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (isOpen) {
      previusActiveElement.current = document.activeElement as HTMLElement;
      document.body.style.overflow = "hidden";

      setTimeout(() => {
        modalRef.current?.focus();
      }, 0);
    } else {
      document.body.style.overflow = "";
      previusActiveElement.current?.focus();
    }

    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onRequestClose();
      } else if (event.key === "Tab") {
        const focusableElements =
          modalRef.current?.querySelectorAll<HTMLElement>(
            'a[href], button:not([disabled]), textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select',
          );

        if (focusableElements && focusableElements.length > 0) {
          const firstElement = focusableElements[0];
          const lastElement = focusableElements[focusableElements.length - 1];

          if (!event.shiftKey && document.activeElement === lastElement) {
            event.preventDefault();
            firstElement.focus();
          } else if (
            event.shiftKey &&
            document.activeElement === firstElement
          ) {
            event.preventDefault();
            lastElement.focus();
          }
        }
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown as EventListener);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown as EventListener);
    };
  }, [isOpen, onRequestClose]);

  const handleOverlayClick = (event: React.MouseEvent) => {
    if (shouldCloseOnOverlayClick && event.target === event.currentTarget) {
      onRequestClose();
    }
  };

  if (!isOpen) return null;

  return createPortal(
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1, transition: { duration: 0.2 } }}
      id="modal"
      role="dialog"
      aria-modal="true"
      className={`fixed inset-0 z-50 flex items-center justify-center bg-black/50 ${overlayClassName}`}
      onClick={handleOverlayClick}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1, transition: { duration: 0.2 } }}
        exit={{ scale: 0.9, opacity: 0, transition: { duration: 0.2 } }}
        className={`relative rounded-[10px] bg-secondaryBackground text-mainText ${className} outline-none focus:outline-none`}
        ref={modalRef}
        tabIndex={-1}
      >
        {children}
      </motion.div>

      <Toaster position="top-right" />
    </motion.div>,
    document.body,
  );
};
