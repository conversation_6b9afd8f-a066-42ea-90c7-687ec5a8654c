"use client";
import { removeThemeCookie } from "@/lib/cookies/themeCookies";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import { FaGear } from "react-icons/fa6";
import { CookieModal } from "./CookieModal";

export const CookieConsent = () => {
  const [showConsent, setShowConsent] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [isClosing, setIsClosing] = useState(false);

  useEffect(() => {
    const checkConsent = setTimeout(() => {
      const hasConsented = localStorage.getItem("cookieConsent");
      if (!hasConsented) {
        setShowConsent(true);
      }
    }, 500);

    return () => clearTimeout(checkConsent);
  }, []);

  const handleCloseConsent = useCallback(() => {
    setIsClosing(true);

    setTimeout(() => {
      setShowConsent(false);
      setIsClosing(false);
    }, 300);
  }, []);

  const handleAcceptAll = useCallback(() => {
    localStorage.setItem(
      "cookieConsent",
      JSON.stringify({ auth: true, theme: true }),
    );
    handleCloseConsent();
  }, [handleCloseConsent]);

  const handleSaveSettings = useCallback(
    (settings: { auth: boolean; theme: boolean }) => {
      localStorage.setItem("cookieConsent", JSON.stringify(settings));
      handleCloseConsent();

      if (!settings.theme) {
        removeThemeCookie();
      }
    },
    [handleCloseConsent],
  );

  const handleOpenSettings = useCallback(() => {
    setShowModal(true);
  }, []);

  if (!showConsent) return null;

  return (
    <>
      <section
        className={`fixed max-w-2xl p-4 mx-auto bg-bgPrimaryWhite border border-PormadeGreen md:gap-x-4 left-12 bottom-6 md:flex md:items-center rounded-2xl shadow-lg transition-opacity duration-300 ${isClosing ? "opacity-0" : "opacity-100"}`}
        role="dialog"
        aria-labelledby="cookie-consent-title"
      >
        <div className="flex items-center gap-x-4">
          <span className="inline-flex p-2 text-green-500  rounded-lg shrink-0 bg-PormadeGray">
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                d="M17.9803 8.5468C17.5123 8.69458 17.0197 8.7931 16.5271 8.7931C14.2118 8.76847 12.3399 6.89655 12.3153 4.58128C12.3153 4.13793 12.3892 3.69458 12.537 3.27586C11.9951 2.68473 11.6995 1.92118 11.6995 1.13301C11.6995 0.812808 11.7488 0.492611 11.8473 0.172414C11.2315 0.0738918 10.6158 0 10 0C4.48276 0 0 4.48276 0 10C0 15.5172 4.48276 20 10 20C15.5172 20 20 15.5172 20 10C20 9.77833 20 9.55665 19.9754 9.33498C19.2611 9.26108 18.5468 8.99015 17.9803 8.5468ZM4.58128 7.31527C6.30542 7.31527 6.30542 10.0246 4.58128 10.0246C2.85714 10.0246 2.61084 7.31527 4.58128 7.31527ZM6.05912 15.7635C4.08867 15.7635 4.08867 12.8079 6.05912 12.8079C8.02956 12.8079 8.02956 15.7635 6.05912 15.7635ZM9.01478 1.33005C10.7389 1.33005 10.7389 4.28571 9.01478 4.28571C7.29064 4.28571 7.04434 1.33005 9.01478 1.33005ZM10.2463 8.84237C11.7241 8.84237 11.7241 10.8128 10.2463 10.8128C8.76848 10.8128 9.01478 8.84237 10.2463 8.84237ZM11.9704 16.9458C10.4926 16.9458 10.4926 14.9754 11.9704 14.9754C13.4483 14.9754 13.202 16.9458 11.9704 16.9458ZM16.6503 13.1034C15.4187 13.1034 15.4187 11.133 16.6503 11.133C17.8818 11.133 17.8818 13.1034 16.6503 13.1034Z"
                fill="currentColor"
              />
            </svg>
          </span>
          <div className="text-sm text-textColorPrimary flex flex-col">
            <p id="cookie-consent-title">
              Usamos cookies para garantir que oferecemos a melhor experiência e
              segurança em nosso site.
            </p>
            <Link
              href={"https://lgpd.pormade.com.br/"}
              target="_blank"
              rel="noopener noreferrer"
              className="text-green-500 hover:underline"
              aria-label="Ler política de cookies (abre em uma nova aba)"
            >
              Leia a política de cookies
            </Link>
          </div>
        </div>

        <div className="flex items-center mt-6 gap-x-4 shrink-0 lg:mt-0">
          <button
            onClick={handleOpenSettings}
            className="text-xs w-1/4 h-[45px] md:w-auto font-medium border border-PormadeGreen text-PormadeGreen hover:text-white rounded-lg hover:bg-PormadeGreen px-4 py-2.5 duration-300 transition-colors focus:outline-none focus:ring-0 focus:ring-offset-0 focus:ring-PormadeGreen"
            aria-label="Configurações de cookies"
          >
            <FaGear size={20} />
          </button>
          <button
            onClick={handleAcceptAll}
            className="text-xs w-2/4 h-[45px] md:w-auto font-medium bg-PormadeGreen rounded-lg hover:bg-green-800 text-white px-4 py-2.5 duration-300 transition-colors  focus:ring-green-800"
            aria-label="Aceitar todos os cookies"
          >
            Aceitar todos os cookies
          </button>
        </div>
      </section>
      <CookieModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSave={handleSaveSettings}
      />
    </>
  );
};
