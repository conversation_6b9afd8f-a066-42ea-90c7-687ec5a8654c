"use client";
import { useTheme } from "@/contexts/theme/themeContext";
import React, { useState } from "react";
import { <PERSON><PERSON>oon, LuSun } from "react-icons/lu";
import "./style.css";

export const HeaderTheme: React.FC = () => {
  const { toggleTheme, theme } = useTheme();
  const [isTimeoutActive, setIsTimeoutActive] = useState(false);

  const handleClick = () => {
    if (!isTimeoutActive) {
      setIsTimeoutActive(true);
      toggleTheme();
      setTimeout(() => {
        setIsTimeoutActive(false);
      }, 500);
    }
  };

  return (
    <div className="flex items-center justify-center mr-8">
      <button
        className={`text-3xl cursor-pointer disabled:opacity-30 theme-button ${theme === "light" || !theme ? "moon" : "sun"}`}
        onClick={handleClick}
        disabled={isTimeoutActive}
      >
        {theme === "light" || !theme ? (
          <LuMoon className={`text-textColorPrimary text-3xl`} />
        ) : (
          <LuSun className={`text-textColorPrimary text-3xl`} />
        )}
      </button>
    </div>
  );
};
