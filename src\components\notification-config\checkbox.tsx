import { FaCheck } from "react-icons/fa";

const CheckboxComponent = ({
  text,
  checked,
  setChecked,
}: {
  text: string;
  checked: boolean;
  setChecked: (checked: boolean) => void;
}) => {
  const toggleCheckbox = () => {
    setChecked(!checked);
  };

  return (
    <div className="flex justify-between text-center">
      <p
        className="mr-2 text-textColorPrimary md:text-sm lg:text-base cursor-pointer "
        onClick={toggleCheckbox}
      >
        <strong>{text}</strong>
      </p>
      <label className="inline-flex items-center cursor-pointer">
        <input
          type="checkbox"
          className="hidden cursor-pointer"
          onChange={toggleCheckbox}
        />
        <span
          className=" w-4  h-4 inline-block mr-2 border border-gray-300"
          style={{
            borderRadius: "10%",
            borderColor: checked ? "green" : "gray",
            backgroundColor: "transparent",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            color: "green",
          }}
        >
          {checked && <FaCheck />}
        </span>
      </label>
    </div>
  );
};

export default CheckboxComponent;
