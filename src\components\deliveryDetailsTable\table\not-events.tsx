import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";

export const NoEventsYet = () => {
    const { animationKeyDelivery } = useDetailsProductContext();

    return (
        <main className="w-full  h-full overflow-hidden">
            <div className="w-full flex justify-center items-center h-full ">
                <div className="flex flex-col items-center text-textColorPrimary">
                    <div
                        key={animationKeyDelivery}
                        className="h-full w-full flex-col flex justify-center items-center text-textColorPrimary"
                    >
                        <div className="flex flex-col gap-3 h-full w-full justify-center items-center">
                            <svg
                                className="crossmark"
                                width="96"
                                height="96"
                                viewBox="0 0 32 32"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <circle
                                    className="crossmark-circle"
                                    cx="16"
                                    cy="16"
                                    r="14"
                                    stroke="var(--text-color-primary)"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                                <path
                                    className="crossmark-cross"
                                    d="M10 10L22 22M22 10L10 22"
                                    stroke="var(--text-color-primary)"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                            </svg>
                        </div>
                    </div>
                    <span className="text-textColorPrimary mt-4">Ainda não possui informações de entrega</span>
                </div>
            </div>
        </main>
    );
};

export const NotDelivered = () => {
    const { animationKeyDelivery } = useDetailsProductContext();

    return (
        <main className="w-full  h-full overflow-hidden">
            <div className="w-full flex justify-center items-center h-full ">
                <div className="flex flex-col items-center text-textColorPrimary">
                    <div
                        key={animationKeyDelivery}
                        className="h-full w-full flex-col flex justify-center items-center text-textColorPrimary"
                    >
                        <div className="flex flex-col gap-3 h-full w-full justify-center items-center">
                            <svg
                                className="crossmark"
                                width="96"
                                height="96"
                                viewBox="0 0 32 32"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <circle
                                    className="crossmark-circle"
                                    cx="16"
                                    cy="16"
                                    r="14"
                                    stroke="var(--text-color-primary)"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                                <path
                                    className="crossmark-cross"
                                    d="M10 10L22 22M22 10L10 22"
                                    stroke="var(--text-color-primary)"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                            </svg>
                        </div>
                    </div>
                    <span className="text-textColorPrimary mt-4">Esse pedido não possui entrega</span>
                </div>
            </div>
        </main>
    );
};
