"use server";
import {
  ErrorData,
  handleGlobalErrors,
} from "@/lib/handleErrors/handleRequestErros";

import { apiInstance } from "@/services/api/apiInstance";
import { ProposalDataInterface } from "@/types/proposal";
import { IResponse } from "@/types/utils";

export const proposalRequest = async (
  id: string,
): Promise<IResponse<ProposalDataInterface | ErrorData>> => {
  try {
    const { data, status } = await apiInstance.get<ProposalDataInterface>(
      "/proposal/find",
      {
        headers: {
          "X-Client-ID": id,
        },
        timeout: 60000,
      },
    );

    return { success: true, data, status: status };
  } catch (error) {
    return handleGlobalErrors(error);
  }
};
