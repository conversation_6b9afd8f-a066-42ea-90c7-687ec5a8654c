"use server";
import {
  ErrorData,
  handleGlobalErrors,
} from "@/lib/handleErrors/handleRequestErros";
import { apiInstance } from "@/services/api/apiInstance";
import { DeliveryInterface } from "@/types/deliveries";
import { IResponse } from "@/types/utils";

export const deliveriesRequest = async (
  id: string,
): Promise<IResponse<DeliveryInterface[] | ErrorData>> => {
  try {
    const { data, status } = await apiInstance.get<DeliveryInterface[]>(
      "/delivery/find",
      {
        headers: {
          "X-Client-ID": id,
        },
      },
    );

    return { success: true, data, status: status };
  } catch (error) {
    return handleGlobalErrors(error);
  }
};
