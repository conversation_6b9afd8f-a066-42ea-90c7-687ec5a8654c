"use client";
import { useAcessContext } from "@/contexts/acess/acessContext";
import { useDataContext } from "@/contexts/data/dataContext";
import { ListaProducao } from "@/types/production";
import { useEffect, useState } from "react";
import { formatProductType } from "../detailsProposal/functions/formatProductType";
import { useOrderDetailsComponent } from "../detailsProposal/useOrderDetailsComponent";

export const useProductionProgressOld = () => {
  const { productionData } = useDataContext();
  const { currentProductType } = useOrderDetailsComponent();
  const [currentPercent, setCurrentPercent] = useState<number | null>(null);
  const { currentProductionSelected } = useAcessContext();

  useEffect(() => {
    if (!currentProductType || !productionData) {
      setCurrentPercent(null);
      return;
    }
    const name = formatProductType(currentProductType);

    const currentProduction = productionData.find(
      (item) => item.order === currentProductionSelected,
    );

    let foundProduct: ListaProducao | undefined;
    if (currentProduction) {
      foundProduct = currentProduction.productionList.find(
        (item) => item.productType === name,
      );
    } else if (!currentProduction && !productionData[0].order) {
      foundProduct = productionData[0].productionList.find(
        (item) => item.productType === name,
      );
    }
    if (foundProduct) {
      setCurrentPercent(foundProduct.percentageCompleted);
    } else {
      setCurrentPercent(null);
    }
  }, [currentProductType, productionData, currentProductionSelected]);

  return {
    currentProductType,
    currentPercent,
  };
};
