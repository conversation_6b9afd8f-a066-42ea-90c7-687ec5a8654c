import { useTheme } from "@/contexts/theme/themeContext";
import { PercentCircleProps } from "@/types/production";
import React, { useEffect, useRef, useState } from "react";
import "./index.module.css";

const OldPercentage: React.FC<PercentCircleProps> = ({
  percentage,
  raio: radius = 25,
  strokeWidth = 8,
  clockwise = true,
  animationSpeed = 10,
  currentProductType,
  showLegend = false,
  fontSize = 2,
}) => {
  const { theme } = useTheme();

  const circumference = 2 * Math.PI * radius;

  const [animatedPercentage, setAnimatedPercentage] = useState(0);
  const circleRef = useRef<SVGCircleElement>(null);

  useEffect(() => {
    setAnimatedPercentage(0);
    if (circleRef.current) {
      circleRef.current.style.strokeDashoffset = `${circumference}px`;
    }

    let currentPercentage = 0;

    const animationInterval = setInterval(() => {
      setAnimatedPercentage(currentPercentage);

      const progress = (currentPercentage / 100) * circumference;
      const offset = clockwise ? circumference - progress : progress;
      if (circleRef.current) {
        circleRef.current.style.strokeDashoffset = `${offset}px`;
      }

      if (currentPercentage >= percentage) {
        clearInterval(animationInterval);
      } else {
        currentPercentage = Math.min(
          Number(percentage.toFixed(1)),
          currentPercentage + 1,
        );
      }
    }, animationSpeed);

    return () => {
      clearInterval(animationInterval);
    };
  }, [
    percentage,
    circumference,
    clockwise,
    animationSpeed,
    currentProductType,
  ]);

  const legendSize = 15;
  const legendSpacing = 5;
  const legendYOffset = radius * 2 + strokeWidth * 2 + 20;

  return (
    <div className="w-full h-full flex justify-center items-center">
      <svg
        width={radius * 2 + strokeWidth * 2}
        height={radius * 2 + strokeWidth * 2 + 50}
        viewBox={`0 0 ${radius * 2 + strokeWidth * 2} ${radius * 2 + strokeWidth * 2 + 50}`}
        style={{
          fill: "none",
          stroke: "none",
          strokeWidth: 0,
          display: "inline",
        }}
      >
        <circle
          cx={radius + strokeWidth}
          cy={radius + strokeWidth}
          r={radius}
          stroke={`${theme === "light" ? "#d3d7ce" : "#2d2c2c"}`}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        <circle
          ref={circleRef}
          cx={radius + strokeWidth}
          cy={radius + strokeWidth}
          r={radius}
          stroke="#079D3A"
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          fill="transparent"
          strokeDashoffset={circumference}
          transform={`rotate(-90 ${radius + strokeWidth} ${radius + strokeWidth})`}
          className="percentage-circle-animation"
        />

        <text
          x={radius + strokeWidth}
          y={radius + strokeWidth}
          dominantBaseline="middle"
          textAnchor="middle"
          fill={theme === "light" ? "#000" : "#fff"}
          style={{ fontWeight: "bold", fontSize: `${fontSize}rem` }}
        >
          {animatedPercentage}%
        </text>

        {showLegend && (
          <>
            <rect
              x={50}
              y={legendYOffset - 2}
              width={legendSize}
              height={legendSize}
              style={{ stroke: "#000", strokeWidth: 0.5 }}
              fill="#079D3A"
            />
            <text
              x={50 + legendSize + legendSpacing}
              y={legendYOffset + legendSize / 2}
              dominantBaseline="middle"
              fill={theme === "light" ? "#000" : "#fff"}
            >
              Concluído
            </text>
            <rect
              x={150 + legendSpacing + legendSize * 3}
              y={legendYOffset - 2}
              width={legendSize}
              height={legendSize}
              style={{ stroke: "#000", strokeWidth: 0.5 }}
              fill={`${theme === "light" ? "#d3d7ce" : "#2d2c2c"}`}
            />
            <text
              x={150 + legendSpacing + legendSize * 4 + legendSpacing}
              y={legendYOffset + legendSize / 2}
              dominantBaseline="middle"
              fill={theme === "light" ? "#000" : "#fff"}
            >
              Pendente
            </text>
          </>
        )}
      </svg>
    </div>
  );
};

export default OldPercentage;
