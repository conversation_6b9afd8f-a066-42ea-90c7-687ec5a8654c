import { StatusEnum } from "@/components/admin/dashboard/constants";

export const AUTH_ROUTES = {
  LOGIN: "/auth/admin-login",
  LOGOUT: "/auth/admin-logout",
  REFRESH: "/auth/admin-refresh",
};

export const TRACKING_ROUTES = {
  FIND_ALL: ({
    limit,
    proposal,
    statusId,
    finished,
    lastUpdateDate,
  }: {
    limit: number;
    proposal?: string;
    statusId?: StatusEnum;
    finished?: boolean;
    lastUpdateDate?: Date;
  }) => {
    const params = new URLSearchParams({
      limit: limit.toString(),
      ...(proposal && { proposal }),
      ...(statusId !== undefined && { statusId: statusId.toString() }),
      ...(finished !== undefined && { finished: finished.toString() }),
      ...(lastUpdateDate && {
        lastUpdate: lastUpdateDate.toISOString().split("T")[0],
      }),
    });

    return `/tracking/find-all?${params.toString()}`;
  },

  GENERATE_TRACKING_LINK: ({ id }: { id: number }) =>
    `/tracking/${id}/generate-tracking-link`,
};
