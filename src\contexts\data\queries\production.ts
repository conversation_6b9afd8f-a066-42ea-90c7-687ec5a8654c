"use client";

import { ProductionRequest } from "@/types/production";
import { useQuery } from "@tanstack/react-query";
import React, { useEffect } from "react";
import { fetchProduction } from "../functions/fetchProduction";

export const useProduction = (
  isAuthenticated: boolean,
  setCurrentProductionSelected: React.Dispatch<
    React.SetStateAction<string | null>
  >,
) => {
  const {
    data: productionData,
    isSuccess: productionSuccess,
    isLoading: productionLoading,
    error: productionError,
    isFetched: productionFetched,
    refetch: productionRefetch,
  } = useQuery<ProductionRequest[] | null | undefined>({
    enabled: isAuthenticated,
    retry: false,
    queryKey: ["production"],
    queryFn: () => fetchProduction(),
  });

  useEffect(() => {
    if (productionData) {
      setCurrentProductionSelected(productionData[0]?.order ?? null);
    }
  }, [productionData, setCurrentProductionSelected]);

  return {
    productionData,
    productionSuccess,
    productionLoading,
    productionError,
    productionRefetch,
    productionFetched,
  };
};
