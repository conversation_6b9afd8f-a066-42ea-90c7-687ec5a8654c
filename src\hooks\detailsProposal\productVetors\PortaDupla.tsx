export const PortaDupla = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="646"
    height="629"
    viewBox="0 0 646 629"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="35"
      y="34.7266"
      width="288"
      height="592.274"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="289"
      y="279.739"
      width="12"
      height="34.4457"
      fill="#010101"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="289"
      y="319.926"
      width="12"
      height="22.0069"
      rx="0.5"
      fill="black"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M290 322.34C290 322.064 290.224 321.84 290.5 321.84H299.5C299.776 321.84 300 322.064 300 322.34V340.476C300 340.752 299.776 340.976 299.5 340.976H290.5C290.224 340.976 290 340.752 290 340.476V322.34Z"
      fill="#202020"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="35"
      y="35"
      width="288"
      height="592.274"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="323"
      y="35"
      width="288"
      height="592.274"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="301"
      y="341"
      width="12"
      height="22.0069"
      rx="0.5"
      fill="black"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M302 343.414C302 343.138 302.224 342.914 302.5 342.914H311.5C311.776 342.914 312 343.138 312 343.414V361.551C312 361.827 311.776 362.051 311.5 362.051H302.5C302.224 362.051 302 361.827 302 361.551V343.414Z"
      fill="#202020"
      stroke="black"
      strokeWidth="2"
    />
    <g filter="url(#filter0_d_712_150)">
      <path d="M275 240H287V419H275V240Z" fill="#010101" />
      <path d="M275 240H287V419H275V240Z" stroke="black" strokeWidth="2" />
    </g>
    <g filter="url(#filter1_d_712_150)">
      <path d="M351 240H363V419H351V240Z" fill="#010101" />
      <path d="M351 240H363V419H351V240Z" stroke="black" strokeWidth="2" />
    </g>
    <rect x="611" y="34" width="35" height="594" fill="black" />
    <rect y="31" width="35" height="597" fill="black" />
    <g filter="url(#filter2_i_712_150)">
      <rect
        x="0.5"
        y="0.5"
        width="645"
        height="33.7262"
        fill="black"
        stroke="black"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_712_150"
        x="274"
        y="239"
        width="26"
        height="189"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="8" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_712_150"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_712_150"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_d_712_150"
        x="350"
        y="239"
        width="26"
        height="189"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="8" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_712_150"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_712_150"
          result="shape"
        />
      </filter>
      <filter
        id="filter2_i_712_150"
        x="0"
        y="0"
        width="646"
        height="38.7266"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_712_150"
        />
      </filter>
    </defs>
  </svg>
);
