export const Fechadura = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="507"
    height="419"
    viewBox="0 0 507 419"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="1.5"
      y="1.5"
      width="167"
      height="167"
      rx="8.5"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="3"
    />
    <rect
      x="1.5"
      y="1.5"
      width="167"
      height="167"
      rx="8.5"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="3"
    />
    <g filter="url(#filter0_d_723_151)">
      <rect x="51" y="46" width="452" height="77" rx="10" fill="#D9D9D9" />
      <rect
        x="52.5"
        y="47.5"
        width="449"
        height="74"
        rx="8.5"
        stroke="black"
        strokeWidth="3"
      />
    </g>
    <g filter="url(#filter1_i_723_151)">
      <rect y="249" width="170" height="170" rx="10" fill="#D9D9D9" />
    </g>
    <rect
      x="1.5"
      y="250.5"
      width="167"
      height="167"
      rx="8.5"
      stroke="black"
      strokeWidth="3"
    />
    <g filter="url(#filter2_d_723_151)">
      <g filter="url(#filter3_d_723_151)">
        <rect x="60" y="287" width="49" height="93" rx="15" fill="#D9D9D9" />
        <rect
          x="60"
          y="287"
          width="49"
          height="93"
          rx="15"
          stroke="black"
          strokeWidth="3"
        />
      </g>
      <circle
        cx="84.5"
        cy="310.5"
        r="16"
        fill="#313030"
        stroke="black"
        strokeWidth="3"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_723_151"
        x="47"
        y="46"
        width="460"
        height="96"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="15" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_723_151"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_723_151"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_i_723_151"
        x="0"
        y="249"
        width="170"
        height="174"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_723_151"
        />
      </filter>
      <filter
        id="filter2_d_723_151"
        x="54.5"
        y="285.5"
        width="60"
        height="104"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_723_151"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_723_151"
          result="shape"
        />
      </filter>
      <filter
        id="filter3_d_723_151"
        x="54.5"
        y="285.5"
        width="60"
        height="104"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_723_151"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_723_151"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
