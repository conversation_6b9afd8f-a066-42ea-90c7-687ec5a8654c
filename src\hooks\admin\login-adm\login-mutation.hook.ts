"use client";

import { loginService } from "@/services/admin/requests/login";
import { getFingerprint } from "@/services/visitorId/getVisitorId";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";

export interface ILoginAdminCredentials {
  user: string;
  password: string;
}

export const useLoginMutation = () => {
  const navigate = useRouter();

  const { mutate } = useMutation({
    mutationKey: ["login"],
    mutationFn: async (data: ILoginAdminCredentials) => {
      const id = await getFingerprint();
      const response = await loginService({
        user: data.user,
        password: data.password,
        clientId: id,
      });

      if (!response?.success) {
        toast.dismiss();
        toast.error(response?.data.message);

        return;
      } else if (response?.success) {
        toast.dismiss();
        toast.success(response?.data.message);

        navigate.push("/admin/dashboard");
      }
    },
  });

  return {
    login: mutate,
  };
};
