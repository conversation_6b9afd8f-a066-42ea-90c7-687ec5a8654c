import { logResponseTime } from "@/lib/colorLog";
import { getCookies } from "@/lib/cookies/saveCookies";
import { CustomAxiosRequestConfig } from "@/types/apiInstance";
import axios, { AxiosRequestConfig } from "axios";
import { AUTH_ROUTES } from "../admin/endpoints";
import { refreshAdminService } from "../admin/requests/refresh";
import { refreshLogic } from "./refreshLogic";

const url = process.env.API_URL;
export const MESSAGE_REFRESH_ERROR = "refresh error";

export const apiInstance = axios.create({
  baseURL: url,
  withCredentials: true,
});

export const apiAdmin = axios.create({
  baseURL: url,
  withCredentials: true,
});

export const apiLivre = axios.create({
  baseURL: url,
});

apiInstance.interceptors.request.use(async (config) => {
  const token = await getCookies("access_token");
  if (token) config.headers.Authorization = `Bearer ${token}`;
  (config as MetadataAxiosRequestConfig).metadata = { startTime: new Date() };
  return config;
});

apiInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config as CustomAxiosRequestConfig;
    if (originalRequest.url === "/auth/login" && error.response?.status === 401)
      return Promise.reject(error);
    if (error.response?.status === 401 && !originalRequest._retry) {
      return refreshLogic({
        config: originalRequest,
        response: error.response,
      })
        .then((newRequest) => {
          if (newRequest) return apiInstance(newRequest);
        })
        .catch((refreshError) => Promise.reject(refreshError));
    }
    return Promise.reject(error);
  },
);

interface MetadataAxiosRequestConfig extends AxiosRequestConfig {
  metadata: { startTime: Date };
  _retry?: boolean;
}

apiAdmin.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config as CustomAxiosRequestConfig;

    const loginRoute = AUTH_ROUTES.LOGIN;
    const logoutRoute = AUTH_ROUTES.LOGOUT;

    if (
      originalRequest.url === loginRoute ||
      originalRequest.url === logoutRoute
    )
      return Promise.reject(error);

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshResult = await refreshAdminService();

        if (refreshResult.success) {
          const newToken = await getCookies("tracker_admin_token");

          originalRequest.headers = {
            ...originalRequest.headers,
            Authorization: `Bearer ${newToken}`,
          };

          return apiAdmin(originalRequest);
        } else {
          return Promise.reject({
            ...error,
            message: "Sessão admin expirada. Por favor, faça login novamente.",
          });
        }
      } catch (refreshError) {
        return Promise.reject({
          ...error,
          message: "Sessão admin expirada. Por favor, faça login novamente.",
        });
      }
    }

    return Promise.reject(error);
  },
);

apiInstance.interceptors.response.use((response) => {
  if (process.env.NODE_ENV === "development") {
    const endTime = new Date();
    const duration =
      endTime.getTime() -
      (
        response.config as MetadataAxiosRequestConfig
      ).metadata.startTime.getTime();
    logResponseTime(response.config.url, duration);
  }
  return response;
});

export default apiInstance;
