/* From Uiverse.io by JaydipPrajapati1910 */
.loader {
  width: 44.8px;
  height: 44.8px;
  position: relative;
  transform: rotate(45deg);
}

.loader:before,
.loader:after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 50% 50% 0 50%;
  background: #0000;
  background-image: radial-gradient(
    circle 11.2px at 50% 50%,
    #0000 94%,
    #ff4747
  );
}

.loader:after {
  animation: pulse-animation-map 1s infinite;
  transform: perspective(336px) translateZ(0px);
}

@keyframes pulse-animation-map {
  to {
    transform: perspective(336px) translateZ(168px);
    opacity: 0;
  }
}
