import { refresh } from "@/contexts/acess/functions/refresh";
import { getCookies } from "@/lib/cookies/saveCookies";
import { CustomAxiosRequestConfig } from "@/types/apiInstance";
import { refreshReturn } from "@/types/auth";
import { AxiosRequestConfig, AxiosResponse } from "axios";
import apiInstance, { MESSAGE_REFRESH_ERROR } from "./apiInstance";

const ignoredEndpoints = ["/auth/code"];

export const refreshLogic = async (failedRequest: {
  config: CustomAxiosRequestConfig;
  response: AxiosResponse;
}): Promise<AxiosRequestConfig | void> => {
  try {
    const { config, response } = failedRequest;
    if (ignoredEndpoints.includes(config.url || ""))
      return Promise.resolve(config);
    if (response?.status !== 401) return Promise.resolve(config);
    if (response?.status === 401 && !config._retry) {
      config._retry = true;
      const refreshToken = await getCookies("refresh_token");
      if (!refreshToken) {
        failedRequest.response.data.errorRefreshLogout = true;
        return Promise.reject(new Error(MESSAGE_REFRESH_ERROR));
      }
      const clientId = config.headers && config.headers["X-Client-ID"];
      const refreshResponse = await refresh(refreshToken, clientId);
      if (!refreshResponse.success)
        return Promise.reject(new Error(MESSAGE_REFRESH_ERROR));
      const { data } = refreshResponse;
      const { access_token } = data as refreshReturn;
      apiInstance.defaults.headers.common["Authorization"] =
        `Bearer ${access_token}`;
      return Promise.resolve({
        ...config,
        headers: { ...config.headers, Authorization: `Bearer ${access_token}` },
      });
    }
  } catch (error: unknown) {
    failedRequest.response.data.errorRefreshLogout = true;
    return Promise.reject(new Error(MESSAGE_REFRESH_ERROR));
  }
};
