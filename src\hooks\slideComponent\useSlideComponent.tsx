"use client";

import { useDataContext } from "@/contexts/data/dataContext";
import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";
import useInitialInformation from "@/hooks/inititalInformation/useInitialInformation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useSwipeable } from "react-swipeable";
import DeliveryDetailsTable from "../../components/deliveryDetailsTable";
import ProductionProgressTable from "../../components/productionProgressTable";

export const useSlideComponent = () => {
  const { stage } = useInitialInformation();
  const [activeComponentIndex, setActiveComponentIndex] = useState<number>(0);
  const {
    changeProductTypeButton,
    setAnimationKeyDelivery,
    setAnimationKeyProduction,
  } = useDetailsProductContext();

  const { proposalData } = useDataContext();

  const components: JSX.Element[] = useMemo(() => {
    const productionComponent = <ProductionProgressTable key="production" />;
    const deliveryComponent = <DeliveryDetailsTable key="delivery" />;

    if (stage === "Em Produção" || stage === "Aprovado") {
      return [productionComponent];
    } else if (stage === "Expedição" && !proposalData?.hasDelivery) {
      return [productionComponent];
    } else {
      return [productionComponent, deliveryComponent];
    }
  }, [stage, proposalData?.hasDelivery]);

  const componentToRender = useCallback(() => {
    if (!stage) return 0;

    if (
      stage === "Em Produção" ||
      stage === "Aprovado" ||
      stage === "Manufaturado"
    ) {
      return 0;
    } else if (stage === "Expedição" && !proposalData?.hasDelivery) {
      return 0;
    } else {
      setAnimationKeyDelivery(Math.random());
      return 1;
    }
  }, [stage, proposalData?.hasDelivery, setAnimationKeyDelivery]);

  useEffect(() => {
    setActiveComponentIndex(componentToRender());
  }, [stage, componentToRender]);

  const swipeHandlers = useSwipeable({
    onSwipedLeft: () => {
      if (activeComponentIndex < components.length - 1) {
        setAnimationKeyDelivery(Math.random());

        setActiveComponentIndex(activeComponentIndex + 1);
      }
    },
    onSwipedRight: () => {
      if (activeComponentIndex > 0) {
        setAnimationKeyProduction(Math.random());
        setActiveComponentIndex(activeComponentIndex - 1);
      }
    },
    trackMouse: true,
  });

  useEffect(() => {
    if (changeProductTypeButton !== null) {
      setActiveComponentIndex(0);
    }
  }, [changeProductTypeButton]);

  return {
    swipeHandlers,
    activeComponentIndex,
    setActiveComponentIndex,
    components,
  };
};
