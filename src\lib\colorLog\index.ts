export const colorLog = (message: string, colorCode: string) => {
  console.log(`\x1b[${colorCode}m%s\x1b[0m`, message);
};

export const logResponseTime = (url: string | undefined, duration: number) => {
  if (duration < 200) {
    colorLog(`⚡️ Rota: ${url} - Tempo de resposta: ${duration}ms`, "32");
  } else if (duration < 500) {
    colorLog(`🔵 Rota: ${url} - Tempo de resposta: ${duration}ms`, "34");
  } else if (duration < 1000) {
    colorLog(`🟡 Rota: ${url} - Tempo de resposta: ${duration}ms`, "33");
  } else {
    colorLog(
      `🔴 Rota: ${url} - Tempo de resposta: ${duration / 1000} segundos`,
      "31",
    );
  }
};

export function formatDuration(duration: number): string {
  return duration < 1000 ? `${duration}ms` : `${(duration / 1000).toFixed(2)}s`;
}
