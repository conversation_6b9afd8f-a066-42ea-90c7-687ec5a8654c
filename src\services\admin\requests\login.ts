"use server";

import { verifyExpiration } from "@/contexts/acess/functions/decodedToken";
import { saveCookies } from "@/lib/cookies/saveCookies";
import { handleGlobalErrors } from "@/lib/handleErrors/handleRequestErros";
import { apiAdmin } from "@/services/api/apiInstance";
import { IApiInstance } from "@/types/utils";
import { AUTH_ROUTES } from "../endpoints";

export interface ILoginAdminCredentials {
  user: string;
  password: string;
  clientId: string;
}

interface ILoginAdminReturn {
  access_token: string;
}

export const loginRequest = async ({
  user,
  password,
  clientId,
}: ILoginAdminCredentials): Promise<IApiInstance<ILoginAdminReturn>> => {
  try {
    const { data, status } = await apiAdmin.post<ILoginAdminReturn>(
      AUTH_ROUTES.LOGIN,
      {
        user,
        password,
      },
      {
        headers: {
          "X-Client-ID": clientId,
        },
      },
    );
    return { success: true, data, status: status };
  } catch (error) {
    return handleGlobalErrors(error);
  }
};

export const loginService = async ({
  user,
  password,
  clientId,
}: ILoginAdminCredentials): Promise<IApiInstance<{ message: string }>> => {
  const data = await loginRequest({
    user,
    password,
    clientId,
  });

  if (data.success) {
    const expToken = verifyExpiration(data.data.access_token);
    await saveCookies("tracker_admin_token", data.data.access_token, expToken);
    return {
      success: true,
      data: { message: "Login efetuado com sucesso" },
      status: data.status,
    };
  } else {
    return {
      success: false,
      data: data.data,
      status: data.status,
    };
  }
};
