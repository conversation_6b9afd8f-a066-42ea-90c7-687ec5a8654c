"use server";

import Destination from "./destination";
import OrderNumber from "./orderNumber";
import ClientName from "./orderNumber/clientName";
import ProductionSelect from "./productionSelect";
import ProgressBar from "./ProgressBar";
import ProgressBarMobile from "./ProgressBar/mobile";

const InitialInformation = () => {
  return (
    <div className="flex w-full  flex-col overflow-hidden">
      <div className="flex md:h-[40px] flex-col tl480:flex-row w-full justify-between">
        <div
          id="client"
          className="text-textColorPrimary items-center flex justify-between text-xs sm:text-base"
        >
          <ClientName />
        </div>
        <div
          id="orderNumber"
          className="text-textColorPrimary gap-3  items-center  flex justify-between text-xs sm:text-base"
        >
          <OrderNumber />
          <ProductionSelect />
        </div>
      </div>
      <div className="flex flex-col-reverse lg:flex-row  lg:mr-0 md:mr-0 mt-2 md:gap-4">
        <div id="1" className="lg:w-2/3 ">
          <div
            id="timeline"
            className="bg-bgPrimaryWhite  rounded-lg w-full  md:mr-0 lg:h-[280px] shadow-md"
          >
            <div className="p-3 md:p-5 hidden md:block">
              <h1
                id="status"
                className="font-bold text-base lg:text-3xl text-textGrayInititalInformation    font-main"
              >
                Progresso de Envio
              </h1>
              <div id="progress-bar">
                <ProgressBar />
              </div>
            </div>
            <div className="p-3 h-auto  md:hidden">
              <h1 className="font-bold text-base md:text-3xl text-textGrayInititalInformation  font-main">
                Progresso de Envio
              </h1>
              <div id="progress-bar-mobile" className="p-3 h-full">
                <ProgressBarMobile />
              </div>
            </div>
          </div>
        </div>
        <div id="2" className="mb-2 sm:mb-4 md:mb-0 lg:w-1/3">
          <Destination />
        </div>
      </div>
    </div>
  );
};

export default InitialInformation;
