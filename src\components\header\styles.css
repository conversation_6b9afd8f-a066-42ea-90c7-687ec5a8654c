.menu {
  position: fixed;
  top: 0;
  right: 0;
  height: 100%;
  background: var(--bg-primary-white);
  z-index: 50;
  transform: scale(0);
  transform-origin: top right;
  box-shadow: -2px 0 5px var(--shadow-color-card);
  border-end-start-radius: 300px;
  transition:
    transform 0.3s ease,
    border-radius 0.3s ease;
}

.menu-open {
  transform: scale(1);
  border-end-start-radius: 0px;
}

.menu a {
  text-decoration: none;
  color: white;
}

.menu a:focus {
  outline: 2px solid #fff;
  outline-offset: 2px;
}
@keyframes menuOpen {
  0% {
    transform: scale(0);
    border-end-start-radius: 300px;
  }
  100% {
    transform: scale(1);
    border-end-start-radius: 0px;
  }
}

.no-scroll {
  overflow: hidden;
}

.tooltip-container2 {
  position: relative;
  display: inline-block;
}

.tooltip2 {
  visibility: hidden;
  width: 60px;
  color: var(--text-color-primary);
  text-align: center;
  border-radius: 6px;
  padding: 0 3px;
  position: absolute;
  z-index: 1;
  bottom: 100%;
  left: 25%;
  margin-left: -30px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip3 {
  visibility: hidden;
  width: 60px;
  color: var(--text-color-primary);
  text-align: center;
  border-radius: 6px;
  padding: 0 3px;
  position: absolute;
  z-index: 1;
  bottom: 100%;
  margin-left: -30px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip-container2:hover .tooltip3 {
  visibility: visible;
  opacity: 1;
}

.tooltip-container2:hover .tooltip2 {
  visibility: visible;
  opacity: 1;
}

@media (max-width: 768px) {
  .tooltip2 {
    display: none;
  }
}
