"use client";

import { useAcessContext } from "@/contexts/acess/acessContext";
import { ChangeEvent, useState } from "react";

import { cpfCnpjMask } from "@/lib/masks/cpfCnpjMask";
import { orderNumberMask } from "@/lib/masks/proposalCodeMask";
import toast from "react-hot-toast";

export const UseAccessPage = () => {
  const { accessMutation } = useAcessContext();
  const [cpfCnpj, setCpfCnpj] = useState<string>("");
  const [proposalCode, setProposalCode] = useState<string>("");
  const [showCpfCnpjLabel, setShowCpfCnpjLabel] = useState<boolean>(false);
  const [showProposalCodeLabel, setShowProposalCodeLabel] =
    useState<boolean>(false);
  const [isButtonDisabled, setIsButtonDisabled] = useState<boolean>(false);
  const [proposalCodeError, setProposalCodeError] = useState<string | null>(
    null,
  );

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    toast.dismiss();
    toast.loading("Carregando...");
    event.preventDefault();
    if (!cpfCnpj || !proposalCode) {
      toast.dismiss();
      toast.error("Preencha todos os campos");
      return;
    }

    if (!proposalCode.includes("-")) {
      setProposalCodeError("Digite um pedido válido, ex: 123456-1");
      toast.dismiss();
      return;
    } else {
      setProposalCodeError("");
    }

    setIsButtonDisabled(true);

    setTimeout(() => {
      setIsButtonDisabled(false);
    }, 500);

    const cnpjCpfFormatted = cpfCnpj.replace(/\D/g, "");
    accessMutation.mutate({ cpfCnpj: cnpjCpfFormatted, proposalCode });
  };

  const handleCnpjCpfChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const oldValue = cpfCnpj;
    const newValue = e.target.value.replace(/\D/g, "");
    const cursorPosition = e.target.selectionStart ?? 0;
    const nonNumericCharsBeforeCursor = (
      oldValue.substring(0, cursorPosition).match(/\D/g) || []
    ).length;
    const maskedValue = cpfCnpjMask(newValue);
    setCpfCnpj(maskedValue);
    const adjustment =
      (maskedValue.substring(0, cursorPosition).match(/\D/g) || []).length -
      nonNumericCharsBeforeCursor;
    const newCursorPosition = cursorPosition + adjustment;
    requestAnimationFrame(() => {
      if (e.target != null) {
        e.target.setSelectionRange(newCursorPosition, newCursorPosition);
      }
    });
  };

  const handleProposalCodeChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const maskedValue = orderNumberMask(value);
    setProposalCode(maskedValue);
  };

  return {
    handleSubmit,
    setShowCpfCnpjLabel,
    setShowProposalCodeLabel,
    showCpfCnpjLabel,
    showProposalCodeLabel,
    setCpfCnpj,
    setProposalCode,
    cpfCnpj,
    proposalCode,
    handleCnpjCpfChange,
    handleProposalCodeChange,
    isButtonDisabled,
    proposalCodeError,
  };
};
