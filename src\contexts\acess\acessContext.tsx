"use client";
import {
  ContextAccessProps,
  ContextAccessProviderProps,
  LoginResponse,
  LoginVariables,
} from "@/types/auth";
import {
  useMutation,
  UseMutationResult,
  useQueryClient,
} from "@tanstack/react-query";
import { usePathname, useRouter } from "next/navigation";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { login } from "./functions/login";
import { VerifyToken } from "./functions/verifyToken";

import { getNewProperty } from "@/lib/cookies/getNewProperty";
import { ErrorData } from "@/lib/handleErrors/handleRequestErros";
import { getFingerprint } from "@/services/visitorId/getVisitorId";
import { IResponse } from "@/types/utils";
import { AxiosError } from "axios";
import toast from "react-hot-toast";
import logoutRequest from "./functions/logout";

const AcessContext = createContext<ContextAccessProps | null>(null);

export const AcessProvider: React.FC<ContextAccessProviderProps> = ({
  children,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [hashExpired, setHashExpired] = useState<boolean>(false);
  const [fetchToDeliveries, setFetchToDeliveries] = useState(false);
  const [currentProductionSelected, setCurrentProductionSelected] = useState<
    string | null
  >(null);
  const [isNewProposal, setIsNewProposal] = useState<boolean>(false);

  useEffect(() => {
    const fetchProposalStatus = async () => {
      const data = await getNewProperty();
      setIsNewProposal(data.status);
    };
    fetchProposalStatus();
  }, [isAuthenticated]);

  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();

  useEffect(() => {
    const fetchData = async () => {
      if (pathname === "/dashboard") {
        const response = await VerifyToken();
        if (response) {
          setIsAuthenticated(true);
        } else {
          setIsAuthenticated(false);
          router.push("/access-page");
        }
      }
    };
    fetchData();
  }, [pathname, router]);

  const logoutMutation: UseMutationResult<
    IResponse<boolean | ErrorData>,
    AxiosError
  > = useMutation({
    mutationKey: ["logout"],
    mutationFn: async (): Promise<IResponse<boolean | ErrorData>> => {
      const id = await getFingerprint();
      const data = await logoutRequest(id);
      if (!data.success) {
        const errorData = data.data as ErrorData;
        throw new Error(errorData.message);
      }
      return data;
    },
    onSuccess: () => {
      setIsAuthenticated(false);
      setFetchToDeliveries(false);
      queryClient.clear();
      router.push("/access-page");
    },
  });

  const logout = useCallback(async () => {
    setIsAuthenticated(false);
    setCurrentProductionSelected(null);
    logoutMutation.mutate(undefined);
    setFetchToDeliveries(false);
    queryClient.clear();
    router.push("/access-page");
  }, [queryClient, router, setFetchToDeliveries, logoutMutation]);

  const accessMutation: UseMutationResult<
    LoginResponse,
    AxiosError,
    LoginVariables
  > = useMutation({
    mutationKey: ["access"],
    mutationFn: async (data: LoginVariables): Promise<LoginResponse> => {
      const id = await getFingerprint();
      return login(data.cpfCnpj, data.proposalCode, id);
    },
    onSuccess: (response: LoginResponse) => {
      if (response.success) {
        const data = response.data as boolean;
        if (data) {
          router.push("/dashboard");
        }
      } else {
        const data = response.data as ErrorData;
        toast.dismiss();
        toast.error(data.message);
      }
    },
  });

  const value = useMemo(
    () => ({
      login,
      logout,
      isAuthenticated,
      setIsAuthenticated,
      hashExpired,
      setHashExpired,
      fetchToDeliveries,
      setFetchToDeliveries,
      accessMutation,
      currentProductionSelected,
      setCurrentProductionSelected,
      isNewProposal,
      setIsNewProposal,
    }),
    [
      logout,
      isAuthenticated,
      setIsAuthenticated,
      hashExpired,
      setHashExpired,
      fetchToDeliveries,
      setFetchToDeliveries,
      accessMutation,
      currentProductionSelected,
      setCurrentProductionSelected,
      isNewProposal,
      setIsNewProposal,
    ],
  );

  return (
    <AcessContext.Provider value={value}>{children}</AcessContext.Provider>
  );
};
export const useAcessContext = () => {
  const context = useContext(AcessContext);
  if (context === null) {
    throw new Error("useAcessContext must be used within an AcessProvider");
  }
  return context;
};
