import { useAcessContext } from "@/contexts/acess/acessContext";
import { ProductionRequest } from "@/types/production";

export const useCurrentProduction = () => {
  const { currentProductionSelected } = useAcessContext();

  const getCurrentProduction = ({
    production,
    isNew,
  }: {
    production: ProductionRequest[] | null | undefined;
    isNew: boolean | undefined;
  }) => {
    if (isNew) {
      return production?.find(
        (item) => item.order === currentProductionSelected,
      );
    }
    return production?.[0];
  };

  return { getCurrentProduction };
};
