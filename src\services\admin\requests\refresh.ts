"use server";

import { verifyExpiration } from "@/contexts/acess/functions/decodedToken";
import { getCookies, saveCookies } from "@/lib/cookies/saveCookies";
import { apiAdmin } from "@/services/api/apiInstance";
import { AUTH_ROUTES } from "../endpoints";

export const refreshAdminRequest = async () => {
  const token = await getCookies("tracker_admin_token");
  if (token) {
    const { data, status } = await apiAdmin.post(
      AUTH_ROUTES.REFRESH,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );
    return { success: true, data, status: status };
  }
  return {
    success: false,
    data: { message: "Refresh não encontrado" },
    status: 401,
  };
};

export const refreshAdminService = async () => {
  const data = await refreshAdminRequest();
  if (data.success) {
    const expToken = verifyExpiration(data.data.access_token);
    await saveCookies("tracker_admin_token", data.data.access_token, expToken);
    return {
      success: true,
      data: { message: "Refresh efetuado com sucesso" },
      status: data.status,
    };
  } else {
    return {
      success: false,
      data: data.data,
      status: data.status,
    };
  }
};
