export interface ButtonControlProps {
  onClick: () => void;
  icon: JSX.Element;
}

export const ButtonControl: React.FC<ButtonControlProps> = ({
  onClick,
  icon,
}) => {
  return (
    <button
      onClick={onClick}
      className=" flex justify-center items-center border-0 m-0 p-0 shadow-lg appearance-none relative cursor-pointer bg-[var(--map-control-button)] w-[40px] h-[40px]"
    >
      {icon}
    </button>
  );
};
