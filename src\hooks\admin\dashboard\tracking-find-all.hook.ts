import { StatusEnum } from "@/components/admin/dashboard/constants";
import { trackingFindAll } from "@/services/admin/requests/tracking-find-all";
import { getFingerprint } from "@/services/visitorId/getVisitorId";
import { useQuery } from "@tanstack/react-query";

export const useTrackingFindAll = ({
  limit,
  proposal,
  statusId,
  finished,
  lastUpdateDate,
}: {
  limit: number;
  proposal?: string;
  statusId?: StatusEnum | undefined;
  finished?: boolean;
  lastUpdateDate?: Date;
}) => {
  const { data, isLoading } = useQuery({
    queryKey: [
      "tracking",
      { limit, proposal, statusId, finished, lastUpdateDate },
    ],
    queryFn: async () => {
      const id = await getFingerprint();
      return trackingFindAll({
        limit,
        clientId: id,
        proposal,
        statusId,
        finished,
        lastUpdateDate,
      });
    },
  });
  return {
    data,
    isLoading,
  };
};
