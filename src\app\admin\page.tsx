"use client";

import { ThemeButton } from "@/components/accessPage/themeButton";
import { LoginForm } from "@/components/admin/login/login-form";
import { useTheme } from "@/contexts/theme/themeContext";
import Image from "next/image";
import logoBranca from "../../assets/images/logo_branco.png";
import logoPreta from "../../assets/images/logo_preta.png";
import mapImage from "../../assets/images/map_image.png";
import { GreenGradientTextureBackground } from "../../components/admin/login/gradient";

const AdminLoginPage = () => {
  const { theme } = useTheme();
  const logo = theme === "light" || !theme ? logoPreta : logoBranca;

  return (
    <main className="flex min-h-screen h-screen bg-PormadeGray relative overflow-hidden">
      <aside className="hidden md:flex md:w-1/2 h-full relative">
        <div className="w-full h-full">
          <GreenGradientTextureBackground className="rounded-br-[10rem]" />
          <div className="relative z-10 flex flex-col justify-center items-center w-full h-full p-8">
            <Image
              src={mapImage}
              alt="Mapa com localização"
              id="map-image"
              quality={100}
              loading="lazy"
              style={{
                width: "auto",
                height: "80%",
                objectFit: "contain",
                objectPosition: "center",
              }}
            />
            <div className="text-center">
              <h1 className="text-white text-3xl font-extrabold drop-shadow-lg">
                Pormade Tracker
              </h1>
              <p className="text-white mt-2 max-w-[280px] drop-shadow-sm">
                Área Administrativa
              </p>
            </div>
          </div>
        </div>
      </aside>
      <section className="flex w-full md:w-1/2 justify-center items-center p-6">
        <article
          className="relative z-20 border border-borderItemDetailsProposal sm:max-w-[38rem] mx-w-[250px] w-full h-auto min-h-[31rem] bg-bgPrimaryWhite rounded-lg shadow-xl flex flex-col py-8 px-6 justify-between"
          aria-label="Formulário de Login Administrativo"
        >
          <header className="flex flex-col items-center mb-4">
            <Image src={logo} alt="Logotipo Pormade" width={200} height={100} />
            <section className="text-center flex flex-col justify-center items-center p-4">
              <p className="text-textColorPrimary text-xs tl480:text-sm mb-2 max-h-16">
                Bem-vindo à Área Administrativa do Pormade Tracker! Aqui você
                poderá acompanhar o progresso de todas as suas propostas.
              </p>
            </section>
          </header>
          <LoginForm />
        </article>
      </section>
      <div className="absolute top-4 right-4">
        <ThemeButton />
      </div>
    </main>
  );
};

export default AdminLoginPage;
