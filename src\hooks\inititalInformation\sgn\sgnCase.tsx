import { useDataContext } from "@/contexts/data/dataContext";
import { useActiveDelivery } from "@/hooks/deliveriesDetails/useCurrentDeliveries";
import { useCurrentProduction } from "@/hooks/productionProgress/useCurrentProduction";

export const useNewSgnCase = () => {
  const {
    proposalData,
    productionData,

    deliveriesData,
  } = useDataContext();

  const { getCurrentDelivery } = useActiveDelivery();
  const { getCurrentProduction } = useCurrentProduction();

  const delivery = getCurrentDelivery({
    delivery: deliveriesData,
    isNew: proposalData?.newProposal,
  });

  const isProposalSgn = proposalData?.newProposal;
  const forecastDelivery = delivery?.delivery?.deliveryDate;
  const inTransitDate = delivery?.delivery?.collectionDate;
  const manufacturedDate = getCurrentProduction({
    production: productionData,
    isNew: proposalData?.newProposal,
  })?.manufacturedDate;
  const productionDate = getCurrentProduction({
    production: productionData,
    isNew: proposalData?.newProposal,
  })?.startProductionDate;
  const approvedDate = proposalData?.approvedDate;

  if (isProposalSgn) {
    if (forecastDelivery) return "Entregue";
    if (inTransitDate) return "Em Trânsito";
    if (manufacturedDate) return "Manufaturado";
    if (productionDate) return "Em Produção";
    if (approvedDate) return "Aprovado";
  }

  return null;
};
