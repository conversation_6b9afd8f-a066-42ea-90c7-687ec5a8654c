"use server";
import { removeCookies, saveCookies } from "@/lib/cookies/saveCookies";
import {
  ErrorData,
  handleGlobalErrors,
} from "@/lib/handleErrors/handleRequestErros";
import { apiLivre } from "@/services/api/apiInstance";
import { HashResponse } from "@/types/auth";
import { IResponse } from "@/types/utils";
import { verifyExpiration } from "./decodedToken";

export const fetchHash = async (
  orderHash: string,
  id: string,
): Promise<IResponse<boolean | ErrorData>> => {
  try {
    const response = await apiLivre.post<HashResponse>(
      "auth/code",
      {
        code: orderHash,
      },
      {
        headers: {
          "X-Client-ID": id,
        },
      },
    );

    await removeCookies("access_token");
    await removeCookies("refresh_token");

    const { data } = response;
    const { access_token, refresh_token } = data;
    const expToken = await verifyExpiration(access_token);
    const expRefreshToken = await verifyExpiration(refresh_token);
    if (expToken) saveCookies("access_token", access_token, expToken);
    if (expRefreshToken)
      saveCookies("refresh_token", refresh_token, expRefreshToken);

    return {
      success: true,
      data: true,
      status: response.status,
    };
  } catch (error: unknown) {
    return handleGlobalErrors(error);
  }
};
