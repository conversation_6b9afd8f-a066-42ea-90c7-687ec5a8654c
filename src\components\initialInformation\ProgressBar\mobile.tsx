"use client";
import { useDataContext } from "@/contexts/data/dataContext";
import useInitialInformation from "@/hooks/inititalInformation/useInitialInformation";
import TimelineMobile from "./progressTimeline/timelineMobile";

import Tooltip from "@/components/tooltip";
// import { useAcessContext } from "@/contexts/acess/acessContext";
import { useActiveDelivery } from "@/hooks/deliveriesDetails/useCurrentDeliveries";
import { CiCircleQuestion } from "react-icons/ci";

const ProgressBarMobile = () => {
  const { proposalData, deliveriesData } = useDataContext();
  const { stage, getForecastDelivery } = useInitialInformation();
  // const { currentProductionSelected } = useAcessContext();
  const { getCurrentDelivery } = useActiveDelivery();

  const alturaTimeline =
    proposalData?.hasDelivery && proposalData?.hasProduction
      ? 700
      : !proposalData?.hasDelivery && !proposalData?.hasProduction
        ? 300
        : (!proposalData?.hasDelivery || !proposalData?.hasProduction) && 400;

  // const getCurrentDelivery = deliveriesData?.find(
  //   (item) => item.order === currentProductionSelected,
  // // );
  // const getCurrentDelivery = useMemo(() => {
  //   if (proposalData?.newProposal) {
  //     return deliveriesData?.find(
  //       (item) => item.order === currentProductionSelected,
  //     );
  //   }

  //   return deliveriesData?.[0];
  // }, [deliveriesData, currentProductionSelected, proposalData?.newProposal]);

  return (
    <>
      {proposalData && typeof window !== "undefined" && stage ? (
        <div
          style={{ height: `${alturaTimeline}px` }}
          className={`w-full flex-col flex justify-center`}
        >
          <div
            className={`w-full flex justify-center items-center h-full  py-5`}
          >
            <TimelineMobile />
          </div>

          <div className="flex justify-around">
            {proposalData?.hasDelivery && (
              <h1 className="text-textColorPrimary text-end">
                {proposalData.hasDelivery && stage !== "Entregue" && (
                  <>
                    {getForecastDelivery() ? (
                      <>
                        <strong>
                          {" "}
                          {getCurrentDelivery({
                            delivery: deliveriesData,
                            isNew: proposalData?.newProposal,
                          })?.deliveryByPartner.isPartner
                            ? "Entrega prevista para o parceiro: "
                            : "Entrega prevista para: "}
                        </strong>
                        {getForecastDelivery()}
                      </>
                    ) : (
                      ""
                    )}
                  </>
                )}
              </h1>
            )}
            {getForecastDelivery() && stage !== "Entregue" && (
              <Tooltip text="A previsão está sujeita a alterações. Continue acompanhando as atualizações.">
                <CiCircleQuestion size={29} className="text-yellow-500" />
              </Tooltip>
            )}
          </div>
        </div>
      ) : (
        <ProgressBarLoadingMobile />
      )}
    </>
  );
};

const ProgressBarLoadingMobile = () => {
  return (
    <main className="w-full h-[700px] flex items-center justify-center mb-2">
      <div className={`w-[65%] h-[${95}%]`}>
        <div className="relative w-full h-full flex justify-between">
          <div
            className={`h-full w-[${24}px] max-h-[85%] mt-[18%]  bg-gray-300 animate-pulseLoading`}
          ></div>
          <div className="h-full w-full flex-col items-baseline">
            <div className="h-1/6 flex w-full items-center">
              <div
                style={{
                  width: `60px`,
                  height: `60px`,
                }}
                className={`absolute flex justify-center items-cente animate-pulseLoading rounded-full bg-gray-300 left-[-20px]`}
              ></div>

              <div
                style={{ marginLeft: `15px` }}
                className={`w-full flex-col flex justify-center  items-center`}
              >
                <p
                  className={`text-center w-[100px] h-4 mb-1 animate-pulseLoading text-black`}
                ></p>
                <hr className="w-full h-[3px] bg-gray-300 animate-pulseLoading" />
                <p className="text-xs mt-[4px] animate-pulseLoading h-3 w-[80px] text-black"></p>
              </div>
            </div>
            <div className="h-1/6 flex w-full items-center">
              <div
                style={{
                  width: `60px`,
                  height: `60px`,
                }}
                className={`absolute flex justify-center items-cente animate-pulseLoading rounded-full bg-gray-300 left-[-20px]`}
              ></div>

              <div
                style={{ marginLeft: `15px` }}
                className={`w-full flex-col flex justify-center  items-center`}
              >
                <p
                  className={`text-center w-[100px] h-4 mb-1 animate-pulseLoading text-black`}
                ></p>
                <hr className="w-full h-[3px] bg-gray-300 animate-pulseLoading" />
                <p className="text-xs mt-[4px] animate-pulseLoading h-3 w-[80px] text-black"></p>
              </div>
            </div>
            <div className="h-1/6 flex w-full items-center">
              <div
                style={{
                  width: `60px`,
                  height: `60px`,
                }}
                className={`absolute flex justify-center items-cente animate-pulseLoading rounded-full bg-gray-300 left-[-20px]`}
              ></div>

              <div
                style={{ marginLeft: `15px` }}
                className={`w-full flex-col flex justify-center  items-center`}
              >
                <p
                  className={`text-center w-[100px] h-4 mb-1 animate-pulseLoading text-black`}
                ></p>
                <hr className="w-full h-[3px] bg-gray-300 animate-pulseLoading" />
                <p className="text-xs mt-[4px] animate-pulseLoading h-3 w-[80px] text-black"></p>
              </div>
            </div>
            <div className="h-1/6 flex w-full items-center">
              <div
                style={{
                  width: `60px`,
                  height: `60px`,
                }}
                className={`absolute flex justify-center items-cente animate-pulseLoading rounded-full bg-gray-300 left-[-20px]`}
              ></div>

              <div
                style={{ marginLeft: `15px` }}
                className={`w-full flex-col flex justify-center  items-center`}
              >
                <p
                  className={`text-center w-[100px] h-4 mb-1 animate-pulseLoading text-black`}
                ></p>
                <hr className="w-full h-[3px] bg-gray-300 animate-pulseLoading" />
                <p className="text-xs mt-[4px] animate-pulseLoading h-3 w-[80px] text-black"></p>
              </div>
            </div>
            <div className="h-1/6 flex w-full items-center">
              <div
                style={{
                  width: `60px`,
                  height: `60px`,
                }}
                className={`absolute flex justify-center items-cente animate-pulseLoading rounded-full bg-gray-300 left-[-20px]`}
              ></div>

              <div
                style={{ marginLeft: `15px` }}
                className={`w-full flex-col flex justify-center  items-center`}
              >
                <p
                  className={`text-center w-[100px] h-4 mb-1 animate-pulseLoading text-black`}
                ></p>
                <hr className="w-full h-[3px] bg-gray-300 animate-pulseLoading" />
                <p className="text-xs mt-[4px] animate-pulseLoading h-3 w-[80px] text-black"></p>
              </div>
            </div>
            <div className="h-1/6 flex w-full items-center">
              <div
                style={{
                  width: `60px`,
                  height: `60px`,
                }}
                className={`absolute flex justify-center items-cente animate-pulseLoading rounded-full bg-gray-300 left-[-20px]`}
              ></div>

              <div
                style={{ marginLeft: `15px` }}
                className={`w-full flex-col flex justify-center  items-center`}
              >
                <p
                  className={`text-center w-[100px] h-4 mb-1 animate-pulseLoading text-black`}
                ></p>
                <hr className="w-full h-[3px] bg-gray-300 animate-pulseLoading" />
                <p className="text-xs mt-[4px] animate-pulseLoading h-3 w-[80px] text-black"></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default ProgressBarMobile;
