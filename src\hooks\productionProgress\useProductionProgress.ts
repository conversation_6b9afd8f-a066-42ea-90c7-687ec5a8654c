"use client";
import { useDataContext } from "@/contexts/data/dataContext";
import { useMemo } from "react";
import { useCurrentProduction } from "./useCurrentProduction";

export interface ProductionItem {
  productType: string;
  percentageCompleted: number | null;
  hasProduction: boolean;
}

export const useProductionProgressNewProposal = () => {
  const { productionData } = useDataContext();

  const { getCurrentProduction } = useCurrentProduction();

  const getItems = useMemo((): ProductionItem[] | null => {
    const channel = getCurrentProduction({
      production: productionData,
      isNew: true,
    });
    if (!channel || !Array.isArray(channel.productionList)) {
      return null;
    }

    const items: ProductionItem[] = [];

    channel.productionList.forEach((item) => {
      const nameProduct = item.productType;
      const foundProduct = channel.productionList.find(
        (item) => item.productType === nameProduct,
      );

      if (foundProduct) {
        if (typeof item.percentageCompleted === "number") {
          items.push({
            productType: item.productType,
            percentageCompleted: item.percentageCompleted,
            hasProduction: true,
          });
        } else {
          items.push({
            productType: item.productType,
            percentageCompleted: null,
            hasProduction: false,
          });
        }
      }
    });

    return items;
  }, [productionData, getCurrentProduction]);

  return {
    getItems,
  };
};
