"use client";

import { Item } from "@/types/proposal";

export const calculateTotalValue = async (
  data: { [key: string]: Item[] },
  currentProductType?: string,
): Promise<number> => {
  if (!data) {
    throw new Error("data é null");
  }

  if (!currentProductType) {
    throw new Error("currentProductType é null");
  }

  let totalQuantity = 0;

  for (const type in data) {
    const filtered = type === currentProductType;
    let itemTotal = 0;
    if (filtered) {
      const items = data[type];
      for (const item of items) {
        const quantity = item.amount ?? 0;
        itemTotal += quantity;
      }
    }
    totalQuantity += itemTotal;
  }

  return totalQuantity;
};
