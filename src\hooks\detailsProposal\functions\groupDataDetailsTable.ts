"use server";

import { Item, ProposalDataInterface } from "@/types/proposal";

export type itensGrouped = {
  inititalType: string;
  groupedData: { [key: string]: Item[] };
};

export const groupedDataDetailsTable = async (
  proposal: ProposalDataInterface,
): Promise<itensGrouped> => {
  const sortOrder = (item: Item) => {
    const tipo = item.productType.toLowerCase();

    if (tipo.includes("porta") && tipo !== "porta") return -1;
    if (tipo === "porta") return 0;
    if (tipo.includes("fechadura") || tipo.includes("dobradica")) return 2;
    return 1;
  };

  const handleSpecialCases = (item: Item): string => {
    const tipo = item.productType.toLowerCase();

    if (tipo.includes("batente")) {
      return "Batente";
    }

    if (tipo.includes("guarnição")) {
      return "Guarnição";
    }

    return item.productType;
  };

  const sortedItems = proposal.items
    .slice()
    .sort((a, b) => sortOrder(a) - sortOrder(b));

  const groupedData = sortedItems.reduce(
    (acc: { [key: string]: Item[] }, item) => {
      const itemType = handleSpecialCases(item);

      if (!acc[itemType]) {
        acc[itemType] = [];
      }
      acc[itemType].push(item);
      return acc;
    },
    {} as { [key: string]: Item[] },
  );

  return {
    inititalType: Object.keys(groupedData)[0],
    groupedData,
  };
};
