import {
  ErrorData,
  handleGlobalErrors,
} from "@/lib/handleErrors/handleRequestErros";
import { apiLivre } from "@/services/api/apiInstance";
import { IResponse } from "@/types/utils";

import { verifyExpiration } from "./decodedToken";
import { saveCookies } from "@/lib/cookies/saveCookies";
import { dataAccess, refreshReturn } from "@/types/auth";

export const refresh = async (
  refreshToken: string,
  clientId: string,
): Promise<IResponse<refreshReturn | ErrorData>> => {
  if (!refreshToken) {
    throw new Error("Parametros obrigatorios ausentes");
  }

  try {
    const response = await apiLivre.post<dataAccess>(
      "/auth/refresh",
      {},
      {
        headers: {
          Authorization: `Bearer ${refreshToken}`,
          "X-Client-ID": clientId,
        },
      },
    );

    const data = response.data;
    const expToken = verifyExpiration(data.access_token);
    const expRefreshToken = verifyExpiration(data.refresh_token);

    if (expToken && expRefreshToken) {
      saveCookies("access_token", data.access_token, expToken);
      saveCookies("refresh_token", data.refresh_token, expRefreshToken);
    }

    return {
      success: true,
      data: {
        access_token: data.access_token,
      },
      status: response.status,
    };
  } catch (error) {
    return handleGlobalErrors(error);
  }
};
