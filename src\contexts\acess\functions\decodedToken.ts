export const decodedToken = (token: string) => {
    try {
        const tokenDecodificado = JSON.parse(Buffer.from(token.split(".")[1], "base64").toString());
        return tokenDecodificado;
    } catch (error) {
        return null;
    }
};

export const verifyExpiration = (token: string) => {
    try {
        const tokenDecodificado = decodedToken(token);
        const expToken = tokenDecodificado?.exp;
        return expToken;
    } catch (error) {
        return null;
    }
};
