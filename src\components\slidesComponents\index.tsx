"use client";
import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";
import { useSlideComponent } from "../../hooks/slideComponent/useSlideComponent";
import { CarrouselComponent } from "./carrousel/carrouselComponent";
import { NavigationComponents } from "./carrousel/navigationComponent";
import "./styles.css";

const SliderComponent = () => {
  const {
    components,
    activeComponentIndex,
    swipeHandlers,
    setActiveComponentIndex,
  } = useSlideComponent();
  const { setAnimationKeyDelivery, setAnimationKeyProduction } =
    useDetailsProductContext();

  return (
    <main
      {...(components.length > 1 && swipeHandlers)}
      className="w-full h-full rounded-xl relative flex flex-col min-h-[560px] sm:min-h-[550px]"
      style={{
        position: "relative",
        boxShadow: "0px 0px 5px var(--shadow-color-card)",
      }}
      tabIndex={-1}
    >
      <CarrouselComponent
        components={components}
        activeComponentIndex={activeComponentIndex}
      />
      {components.length > 1 && (
        <NavigationComponents
          components={components}
          activeComponentIndex={activeComponentIndex}
          setActiveComponentIndex={setActiveComponentIndex}
          setAnimationKeyDelivery={setAnimationKeyDelivery}
          setAnimationKeyProduction={setAnimationKeyProduction}
        />
      )}
    </main>
  );
};

export default SliderComponent;
