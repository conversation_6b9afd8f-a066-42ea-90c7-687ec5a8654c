"use client";

import { AccessInput } from "@/components/accessPage/accessInput";
import { useLoginForm } from "@/hooks/admin/login-adm/login-form.hook";
import { useState } from "react";
import { Toaster } from "react-hot-toast";

export const LoginForm = () => {
  const {
    user,
    setUser,
    password,
    setPassword,
    showUserLabel,
    setShowUserLabel,
    showPasswordLabel,
    setShowPasswordLabel,
    handleSubmit,
  } = useLoginForm();

  const handleUserBlur = () => {
    setShowUserLabel(user.length > 0);
  };

  const handlePasswordBlur = () => {
    setShowPasswordLabel(password.length > 0);
  };

  const [showPassword, setShowPassword] = useState(false);

  return (
    <form
      className="space-y-1"
      onSubmit={handleSubmit}
      noValidate
      aria-label="Admin Login Form"
    >
      <fieldset className="space-y-5 mb-8 border-0">
        <AccessInput
          id="user"
          name="user"
          label="Usuário"
          value={user || ""}
          onFocus={() => setShowUserLabel(true)}
          onBlur={handleUserBlur}
          onChange={(e) => setUser(e.target.value)}
          showLabel={showUserLabel}
        />

        <div className="relative">
          <input
            type={showPassword ? "text" : "password"}
            id="passwor-admin"
            name="password"
            value={password || ""}
            onFocus={() => setShowPasswordLabel(true)}
            onBlur={handlePasswordBlur}
            onChange={(e) => setPassword(e.target.value)}
            autoComplete="new-password"
            className="w-full text-textInputLogin bg-bgGrayInputLogin border-b border-transparent p-3 pt-5 pb-2 pr-10 outline-none border-gray-500 autocomplete"
          />

          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-3 flex items-center"
            tabIndex={-1}
          >
            {showPassword ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="var(--text-input-login)"
                className="w-6 h-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7a9.956 9.956 0 012.01-3.307M6.22 6.22a9.953 9.953 0 015.78-2.22c4.477 0 8.268 2.943 9.542 7a9.957 9.957 0 01-2.246 3.564M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 3l18 18"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="var(--text-input-login)"
                className="w-6 h-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M2.458 12C3.732 7.943 7.522 5 12 5c4.477 0 8.268 2.943 9.542 7-1.274 4.057-5.065 7-9.542 7-4.478 0-8.268-2.943-9.542-7z"
                />
              </svg>
            )}
          </button>

          <label
            htmlFor="id-password-admin"
            className={`absolute left-3 transition-all duration-400 ${
              showPasswordLabel
                ? "top-1 text-xs text-labelInputLoginActive cursor-text"
                : "top-3 text-base text-labelInputLoginInactive cursor-text"
            }`}
          >
            Senha
          </label>
        </div>
      </fieldset>

      <button
        type="submit"
        className="w-full bg-PormadeGreen text-textButtonLogin py-4 rounded-md hover:bg-bgButtonLoginHover disabled:opacity-25 font-extrabold"
        style={{
          textShadow: "1px 1px 1px var(--shadow-button-login)",
        }}
      >
        Entrar
      </button>

      <Toaster position="top-right" reverseOrder={false} />
    </form>
  );
};
