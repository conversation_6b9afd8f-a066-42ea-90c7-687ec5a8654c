"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";

const NotFound = () => {
    const router = useRouter();

    const handleRedirect = () => {
        router.push("/access-page");
    };

    return (
        <div className='flex justify-center bg-bgPrimaryWhite flex-col sm:flex-row items-center h-screen'>
            <div className='sm:w-1/2 h-1/2  sm:h-screen flex justify-center items-center fa'>
                <Image
                    src='/assets/images/404-image.svg'
                    className='w-[80%]  h-full'
                    alt='Imagem'
                    width={600}
                    height={600}
                />
            </div>
            <div className='sm:w-1/2  h-1/2 sm:h-screen p-4 flex sm:justify-center sm:items-center flex-col '>
                <div className='flex flex-col sm:pr-[120px] justify-start items-center'>
                    <h1 className='sm:text-[34px]  text-[24px] md:text-[44px] w-full text-center xl:text-[44px] lg:text-[44px] text-textColorPrimary font-bold lg:mb-[30px]'>
                        Página não encontrada
                    </h1>
                    <p className='md:text-[18px] text-[16px] lg:text-[23px] font-sans font-light text-textColorPrimary mt-4 text-center'>
                        Oops! Não conseguimos encontrar a página que você está
                        procurando. Pode ter sido removida, teve seu nome
                        alterado, ou está temporariamente indisponível.
                    </p>

                    <button
                        className='bg-PormadeGreen text-white w-[250px] px-5 py-3 mt-8 rounded-md'
                        onClick={handleRedirect}
                    >
                        Ir para tela de acesso
                    </button>
                </div>
            </div>
        </div>
    );
};

export default NotFound;
