import { ReactElement } from "react";
import { Acessorios } from "./productVetors/Acessorios";
import { Bandeira } from "./productVetors/Bandeira";
import { Batente } from "./productVetors/Batente";
import { Biombo } from "./productVetors/Biombo";
import { Dobradica } from "./productVetors/Dobradica";
import { Esquadria } from "./productVetors/Esquadrias";
import { Fechadura } from "./productVetors/Fechadura";
import { FechaduraRolete } from "./productVetors/FechaduraRolete";
import { FolhaPorta } from "./productVetors/FolhaPorta";
import { Guarnicao } from "./productVetors/Guarnicao";
import { PapelParedeImage } from "./productVetors/PapelParede";
import { PisoVinilico } from "./productVetors/PisoVinilico";
import { PortaAluminio } from "./productVetors/PortaAluminio";
import { PortaCorrer } from "./productVetors/PortaCorrer";
import { PortaDupla } from "./productVetors/PortaDupla";
import { PortaDuplaCorrer } from "./productVetors/PortaDuplaCorrer";
import { PortaPivotante } from "./productVetors/PortaPivotante";
import { PortaPronta } from "./productVetors/PortaPronta";
import { PortaTrilhoAparente } from "./productVetors/PortaTrilhoAparente";
import { PortaVaiVemDupla } from "./productVetors/PortaVaiEVemDupla";
import { PortaVaiVem } from "./productVetors/PortaVaiVem";
import { Rodape } from "./productVetors/Rodape";
import { Servicos } from "./productVetors/Servicos";

export type PermittedTypes =
  | "MARCO"
  | "ALIZAR"
  | "FECHADURA"
  | "PORTA"
  | "ACESSORIOS"
  | "BANDEIRA"
  | "DOBRADICA"
  | "PORTA PRONTA"
  | "PORTA DE CORRER"
  | "RODAPE"
  | "SERVICOS"
  | "PAPEL DE PAREDE"
  | "PISO VINILICO"
  | "PORTA PIVOTANTE"
  | "GUARNIÇÃO"
  | "BIOMBO"
  | "PORTA DUPLA"
  | "PORTA DUPLA DE CORRER"
  | "PORTA TRILHO APARENTE"
  | "PORTA VAI E VEM"
  | "PORTA VAI E VEM DUPLA"
  | "ESQUADRIA"
  | "PORTA ALUMÍNIO"
  | "ACESSÓRIOS"
  | "RODAPÉ";

export const useDetailsContent = () => {
  const ImageType: Record<PermittedTypes, ReactElement> = {
    "PAPEL DE PAREDE": <PapelParedeImage />,
    "PORTA PRONTA": <PortaPronta />,
    "PORTA DE CORRER": <PortaCorrer />,
    DOBRADICA: <Dobradica />,
    FECHADURA: <Fechadura />,
    BIOMBO: <Biombo />,
    "PORTA PIVOTANTE": <PortaPivotante />,
    PORTA: <FolhaPorta />,
    "PISO VINILICO": <PisoVinilico />,
    SERVICOS: <Servicos />,
    RODAPE: <Rodape />,
    RODAPÉ: <Rodape />,
    MARCO: <Batente />,
    ALIZAR: <Guarnicao />,
    GUARNIÇÃO: <Guarnicao />,
    BANDEIRA: <Bandeira />,
    ACESSORIOS: <Acessorios />,
    ACESSÓRIOS: <Acessorios />,
    "PORTA DUPLA": <PortaDupla />,
    "PORTA DUPLA DE CORRER": <PortaDuplaCorrer />,
    "PORTA TRILHO APARENTE": <PortaTrilhoAparente />,
    "PORTA VAI E VEM": <PortaVaiVem />,
    "PORTA VAI E VEM DUPLA": <PortaVaiVemDupla />,
    ESQUADRIA: <Esquadria />,
    "PORTA ALUMÍNIO": <PortaAluminio />,
  };

  const formatType = (type: string) => {
    switch (type.toLowerCase()) {
      case "batente":
      case "perna de batente":
      case "cabeceira de batente":
        return "Marco";
      case "guarnicao":
      case "perna de guarnicao":
      case "cabeceira de guarnicao":
        return "Alizar";
      default:
        return type;
    }
  };

  const fetchComponent = (
    productType: string,
    description?: string,
  ): ReactElement => {
    if (description && description.toLowerCase().includes("rolete")) {
      return <FechaduraRolete />;
    }
    const type = formatType(productType).toUpperCase() as PermittedTypes;
    if (type in ImageType) {
      return ImageType[type];
    } else {
      return <h1 className="text-black">Sem Imagem</h1>;
    }
  };

  return {
    fetchComponent,
    formatType,
    ImageType,
  };
};
