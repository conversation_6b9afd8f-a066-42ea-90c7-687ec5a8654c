import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  ITrackingProposal,
  StatusStringType,
} from "@/services/admin/requests/tracking-find-all";
import { createElement } from "react";
import { getColorForStatus, STATUSES } from "./constants";
import { ProposalCard } from "./status-card";

export const AccordionCards = ({
  status,
  proposals,
  onSelectProposal,
  isLoading,
}: {
  status: StatusStringType;
  proposals: ITrackingProposal[];
  onSelectProposal: (proposal: ITrackingProposal) => void;
  isLoading: boolean;
}) => {
  const statusConfig = STATUSES.find((s) => s.label === status);
  const Icon = statusConfig?.icon ?? (() => null);
  const borderColor = getColorForStatus(status);

  return (
    <AccordionItem
      value={status}
      style={{ borderColor }}
      aria-label={`Status card accordion for ${status}`}
      className="md:min-w-[280px] min-w-full flex md:w-[250px] w-full flex-col justify-between p-3 bg-bgPrimaryWhite rounded-lg shadow-sm border-[0.2px]"
    >
      <AccordionTrigger>
        <header className="flex justify-between w-full">
          <h2 className="text-xl font-semibold text-center flex items-center justify-center gap-2">
            {createElement(Icon as React.FC<{ className?: string }>, {
              className: "w-6 h-6",
            })}
            {status}
          </h2>
          <p>
            {proposals.length} {proposals.length > 1 ? "propostas" : "proposta"}
          </p>
        </header>
      </AccordionTrigger>
      <AccordionContent>
        <div className="flex-1 gap-3 flex flex-col">
          {isLoading ? (
            <>carregando....</>
          ) : proposals.length > 0 ? (
            proposals.map((proposal) => (
              <ProposalCard
                key={proposal.proposal}
                proposal={proposal}
                borderColor={borderColor}
                onSelect={onSelectProposal}
              />
            ))
          ) : (
            <p className="text-sm text-gray-400 text-center">
              Nenhuma proposta
            </p>
          )}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};
