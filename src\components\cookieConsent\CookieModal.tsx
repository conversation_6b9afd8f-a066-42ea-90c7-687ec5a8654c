"use client";

import { useState } from "react";
import ReactDOM from "react-dom";
import { IoMdClose } from "react-icons/io";
import "./checkbox.css";

interface CookieModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (settings: { auth: boolean; theme: boolean }) => void;
}

export const CookieModal = ({ isOpen, onClose, onSave }: CookieModalProps) => {
  const [settings, setSettings] = useState({
    auth: true,
    theme: true,
  });

  if (!isOpen) return null;

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return ReactDOM.createPortal(
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"
      onClick={handleOverlayClick}
    >
      <div className="bg-bgPrimaryWhite p-6 rounded-lg max-w-2xl w-full mx-4 border border-PormadeGreen">
        <div className="flex justify-between items-center mb-4">
          <div className="flex gap-2 items-center">
            <h2 className="text-xl font-semibold text-white">
              Configurações de Cookies
            </h2>
            <span className="inline-flex p-2 text-green-500  rounded-lg shrink-0 ">
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M17.9803 8.5468C17.5123 8.69458 17.0197 8.7931 16.5271 8.7931C14.2118 8.76847 12.3399 6.89655 12.3153 4.58128C12.3153 4.13793 12.3892 3.69458 12.537 3.27586C11.9951 2.68473 11.6995 1.92118 11.6995 1.13301C11.6995 0.812808 11.7488 0.492611 11.8473 0.172414C11.2315 0.0738918 10.6158 0 10 0C4.48276 0 0 4.48276 0 10C0 15.5172 4.48276 20 10 20C15.5172 20 20 15.5172 20 10C20 9.77833 20 9.55665 19.9754 9.33498C19.2611 9.26108 18.5468 8.99015 17.9803 8.5468ZM4.58128 7.31527C6.30542 7.31527 6.30542 10.0246 4.58128 10.0246C2.85714 10.0246 2.61084 7.31527 4.58128 7.31527ZM6.05912 15.7635C4.08867 15.7635 4.08867 12.8079 6.05912 12.8079C8.02956 12.8079 8.02956 15.7635 6.05912 15.7635ZM9.01478 1.33005C10.7389 1.33005 10.7389 4.28571 9.01478 4.28571C7.29064 4.28571 7.04434 1.33005 9.01478 1.33005ZM10.2463 8.84237C11.7241 8.84237 11.7241 10.8128 10.2463 10.8128C8.76848 10.8128 9.01478 8.84237 10.2463 8.84237ZM11.9704 16.9458C10.4926 16.9458 10.4926 14.9754 11.9704 14.9754C13.4483 14.9754 13.202 16.9458 11.9704 16.9458ZM16.6503 13.1034C15.4187 13.1034 15.4187 11.133 16.6503 11.133C17.8818 11.133 17.8818 13.1034 16.6503 13.1034Z"
                  fill="currentColor"
                />
              </svg>
            </span>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-200"
          >
            <IoMdClose size={24} />
          </button>
        </div>

        <div className="space-y-4 text-white">
          <div className="border-b border-gray-700 pb-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-white">
                  Cookies Essenciais (Autenticação)
                </h3>
                <p className="text-sm text-gray-400">
                  Necessário para acessar o sistema. Não pode ser desativado.
                </p>
              </div>

              <div className="checkbox-wrapper-46">
                <input
                  checked={true}
                  readOnly
                  type="checkbox"
                  id="cbx-46"
                  className="inp-cbx"
                />
                <label htmlFor="cbx-46" className="cbx">
                  <span className="opacity-40">
                    <svg viewBox="0 0 12 10" height="10px" width="12px">
                      <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                    </svg>
                  </span>
                </label>
              </div>
            </div>
          </div>

          <div className="border-b border-gray-700 pb-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-white">
                  Cookies de Preferências
                </h3>
                <p className="text-sm text-gray-400">
                  Armazena suas preferências de tema (claro/escuro).
                </p>
              </div>
              <div className="flex relative items-center justify-center">
                <div className="checkbox-wrapper-46">
                  <input
                    type="checkbox"
                    id="cbx-47"
                    className="inp-cbx"
                    checked={settings.theme}
                    onChange={(e) =>
                      setSettings({ ...settings, theme: e.target.checked })
                    }
                  />
                  <label htmlFor="cbx-47" className="cbx">
                    <span>
                      <svg viewBox="0 0 12 10" height="10px" width="12px">
                        <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                      </svg>
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm text-gray-400 hover:text-white"
          >
            Cancelar
          </button>
          <button
            onClick={() => {
              onSave(settings);
              onClose();
            }}
            className="px-4 py-2 text-sm bg-PormadeGreen text-white rounded-lg hover:bg-green-800 transition-colors"
          >
            Aceitar cookies selecionados
          </button>
        </div>
      </div>
    </div>,
    document.body,
  );
};
