export interface ProductionRequest {
  manufacturedDate?: string;
  startProductionDate?: string;
  awaitingCollectionDate: string;
  expectedDeliveryDate: string;
  productionList: ListaProducao[];
  order: string;
}

export interface ListaProducao {
  percentageCompleted: number;
  productType: string;
}

export interface PercentCircleProps {
  percentage: number;
  raio?: number;
  strokeWidth?: number;
  clockwise?: boolean;
  animationSpeed?: number;
  showLegend?: boolean;
  fontSize?: number;
  currentProductType?: string;
}
