"use client";

import { getFingerprint } from "@/services/visitorId/getVisitorId";
import { DeliveryInterface } from "@/types/deliveries";
import { deliveriesRequest } from "./deliveriesRequest";
[];
export const fetchDeliveries = async (): Promise<
  DeliveryInterface[] | null
> => {
  const id = await getFingerprint();

  const data = await deliveriesRequest(id);
  if (data.success) {
    return data.data as DeliveryInterface[];
  } else {
    if ("message" in data.data) {
      throw new Error(data.data.message);
    } else {
      throw new Error("Unknown error");
    }
  }
};
