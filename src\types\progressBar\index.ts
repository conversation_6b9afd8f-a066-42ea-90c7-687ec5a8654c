import { ElementType } from "react";

export interface Dates {
  Aprovado: string;
  "Em Produção": string;
  Manufaturado: string;
  Expedição: string;
  "Em Trânsito": string;
  Entregue: string;
}

export interface RenderComponentProps {
  stage: string;
  name: string;
  Icon: ElementType;
  isActiveText: (stage: string) => string;
  isActiveCicle: (stage: string) => string;
  isActiveIcon: (stage: string) => string;
  dates: Dates;
  handleDateStage: (currentStage: string) => void;
}
