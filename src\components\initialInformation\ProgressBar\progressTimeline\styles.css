:root {
  --bar: 50%;
  --animationTime: 3000ms;
  --transitionTime: 1s;
}

.barTransition {
  max-width: 94%;
  /* transition: 1s linear; */
  transition: var(--transitionTime) linear;
  transition-property: width;
  /* 
  animation: barTransition var(--animationTime) linear; */
  animation: none;
}

.barTransitionVertical {
  transition: var(--transitionTime) linear;
  transition-property: height;
  animation: none;

  /* 
  transition-delay: 1s; */
  /* animation: barTransitionVertical var(--animationTime) linear; */
}

@media screen and (min-width: 1024px) {
  .barTransition {
    max-width: 95%;
  }
}

@keyframes barTransitionVertical {
  from {
    height: 0;
  }

  to {
    /* height: calc(var(--bar) + (var(--bar) * 0.05)); */
    height: var(--bar);
  }
}

@keyframes barTransition {
  from {
    width: 0;
  }

  to {
    width: var(--bar);
  }
}

.shadowProgress {
  box-shadow: 0px 0px 0px 0px #10b981;
}

.barProgress {
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.animationActive {
  transition: linear var(--transitionTime);
}

.connector {
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.404);
}

.connector::before {
  content: "";
  position: absolute;
  top: -14px;
  right: -20px;
  width: 50%;
  height: 50%;
  background-color: transparent;
  border-bottom-left-radius: 20px;
  box-shadow: -2px 20px 0px -2px var(--bg-progress-timeline);
  z-index: 1;
}

.connector-left {
  box-shadow: -3px 2px 4px rgb(0 0 0 / 27%);
}

.connector-left::before {
  content: "";
  position: absolute;
  top: -14px;
  left: -20px;
  width: 50%;
  height: 50%;
  background-color: transparent;
  border-bottom-right-radius: 20px;
  box-shadow: 2px 20px 0px -2px var(--bg-progress-timeline);
  z-index: 1;
}

.connector::after {
  content: "";
  position: absolute;
  bottom: -14px;
  right: -22px;
  width: 50%;
  height: 50%;
  background-color: transparent;
  border-top-left-radius: 20px;
  box-shadow: 2px -20px 0px -2px var(--bg-progress-timeline);
  z-index: 1;
}

.connector-left::after {
  content: "";
  position: absolute;
  bottom: -14px;
  left: -22px;
  width: 50%;
  height: 50%;
  background-color: transparent;
  border-top-right-radius: 20px;
  box-shadow: 0px -20px 0px -2px var(--bg-progress-timeline);
  z-index: 1;
}

@media (max-width: 1024px) {
  .connector::before {
    display: none;
  }

  .connector-left::before {
    display: none;
  }

  .connector::after {
    display: none;
  }

  .connector-left::after {
    display: none;
  }
}

.clickSpan {
  transition: 2s linear;
}

.shadowItemActive {
  box-shadow: 0px 0px 0px 0px #10b981;
}

.shadowClass {
  box-shadow: -3px 0px 16px -5px var(--shadow-color-card);
}

.teste {
  transition: 0.1s linear;
}

.transitionLinear {
  transition: 0.3s linear;
  animation: transitionLinear 0.3s linear;
}

@keyframes transitionLinear {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
