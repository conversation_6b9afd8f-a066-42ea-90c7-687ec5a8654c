import ReactDOM from "react-dom";

import { IoNotificationsOutline } from "react-icons/io5";
import { IoMdClose } from "react-icons/io";
import React from "react";
import CheckboxComponent from "./checkbox";
import useNotification from "@/hooks/notification/useNotification";
import { useDataContext } from "@/contexts/data/dataContext";

const NotificationConfig = ({
  setOpen,
}: {
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const {
    notificationElement,
    modalRef,
    emailNotification,
    setEmailNotification,
    updateSubmit,
    handleClickOutside,
    loadingEmail,
    dataEmail,
  } = useNotification(setOpen);
  const { proposalData } = useDataContext();

  return ReactDOM.createPortal(
    <div
      id="notification-config"
      style={{
        zIndex: 9999999,
      }}
      className="w-full h-full fixed bg-black bg-opacity-70  top-0 left-0 "
    >
      <section
        id="modal-notification"
        className="w-full h-full flex justify-center items-center"
        onClick={(e: React.MouseEvent) => {
          e.stopPropagation();
          handleClickOutside(e);
        }}
      >
        <div
          ref={modalRef}
          className="sm:w-[500px] m-2 relative sm:h-[330px] bg-bgPrimaryWhite rounded-3xl p-6 shadow-lg"
        >
          <div className=" top-0 right-0 flex justify-end">
            <button onClick={() => setOpen(false)}>
              <IoMdClose size={20} className="text-textColorPrimary" />
            </button>
          </div>
          <header>
            <h1 className="justify-center w-full flex items-center text-center sm:text-xl font-bold text-green-600 mb-4">
              <IoNotificationsOutline className="text-2xl mr-3" />
              Configurações de Notificação{" "}
              <IoNotificationsOutline className="text-2xl ml-3" />
            </h1>
          </header>
          <div className="w-full mt-5 flex justify-center flex-col">
            <h1 className="text-center sm:text-md font-bold text-textColorPrimary">
              Escolha o tipo de mensagem que deseja receber:
            </h1>
            {loadingEmail ? (
              <>
                <div className="justify-center mt-5 items-center animate-pulseLoading bg-opacity-35  p-5  w-full h-full flex flex-col gap-4">
                  <div className=" w-[80%]"></div>
                </div>
              </>
            ) : (
              <div className="justify-center mt-5 items-center  p-4  w-full h-full flex flex-col gap-4">
                <div className=" w-full ">
                  <CheckboxComponent
                    text="Notificação via Email"
                    checked={emailNotification}
                    setChecked={setEmailNotification}
                  />
                  <p className="text-xs text-start text-textColorPrimary">
                    Enviando para: {proposalData?.email}
                  </p>
                </div>
              </div>
            )}
          </div>
          <article className="w-full flex justify-center mt-5">
            <p className="text-xs text-center text-textColorPrimary">
              Obs: Selecione as opções que deseja receber.
            </p>
          </article>
          <div className="w-full flex justify-center mt-5">
            <button
              onClick={updateSubmit}
              disabled={dataEmail?.email === emailNotification}
              className="w-[80%] h-[40px] bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 disabled:opacity-50 disabled:bg-green-900 rounded"
            >
              Salvar
            </button>
          </div>
        </div>
      </section>
    </div>,
    notificationElement!,
  );
};

export default NotificationConfig;
