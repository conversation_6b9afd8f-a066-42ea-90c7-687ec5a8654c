import { useAcessContext } from "@/contexts/acess/acessContext";
import { useDataContext } from "@/contexts/data/dataContext";
import { DeliveryType } from "@/types/deliveries";
import { useCallback, useEffect, useState } from "react";
import { useActiveDelivery } from "../deliveriesDetails/useCurrentDeliveries";
import { useCurrentProduction } from "../productionProgress/useCurrentProduction";
import { useNewSgnCase } from "./sgn/sgnCase";

const useInitialInformation = () => {
  const {
    proposalData,
    deliveriesData,
    deliveriesFetched,
    productionData,
    productionFetched,
  } = useDataContext();
  const { isNewProposal } = useAcessContext();
  const { currentProductionSelected } = useAcessContext();
  const [stage, setStage] = useState<string | undefined>();
  const sgnStage = useNewSgnCase();
  const { getCurrentDelivery } = useActiveDelivery();
  const { getCurrentProduction } = useCurrentProduction();

  const formatStageName = useCallback(
    (status: string | undefined) => {
      if (!status || !productionFetched) return;

      if (proposalData?.newProposal && productionFetched) {
        if (
          getCurrentProduction({
            production: productionData,
            isNew: proposalData?.newProposal,
          })?.awaitingCollectionDate
        ) {
          return "Expedido";
        }

        return sgnStage;
      }

      const stage = status.split("/")[0];

      if (stage === "Aprovada - Aguardando Pagamento Total") {
        return "Aprovada";
      }

      if (proposalData?.shipmentDate) {
        return "Expedido";
      }

      switch (stage) {
        case "Em Análise Financeira":
        case "Aprovada - Aguardando Pagamento Total":
        case "Aprovada":
        case "Em Análise Medidas Especiais":
        case "Em Revisão":
          return "Aprovado";
        case "Em Programação":
        case "Em Produção/Manuseio":
        case "Enviado Para Programação":
        case "Aguardando Produtos":
        case "Agendado Envio para Programação":
        case "Em Separação Inicial":
          return "Em Produção";
        case "Expedido":
          return "Expedido";
        case "Cancelada":
        case "Estornado / Devolução para o Cliente":
          return "Cancelada";
        default:
          return stage;
      }
    },
    [
      proposalData,
      sgnStage,
      productionFetched,
      getCurrentProduction,
      productionData,
    ],
  );

  useEffect(() => {
    if (currentProductionSelected !== undefined) {
      setStage(undefined);
    }
  }, [currentProductionSelected]);

  const checkCurrentStep = useCallback(
    (stageName: string | undefined, deliveries?: DeliveryType | null) => {
      if (!stageName) return;

      console.log("Staf", {
        deliveriesFetched,
        stageName,
        deliveries,
      });

      if (
        (stageName === "Expedido" || stageName === "Expedição") &&
        deliveries
      ) {
        const situationsFretefy = {
          awaitingCollection: 1,
          canceled: 2,
          delivered: 3,
          inProgress: 4,
          problem: 99,
        };

        switch (deliveries.situation) {
          case situationsFretefy.awaitingCollection:
            return "Expedição";
          case situationsFretefy.canceled:
            return "Cancelada";
          case situationsFretefy.delivered:
            return "Entregue";
          case situationsFretefy.inProgress:
            return "Em Trânsito";
          case situationsFretefy.problem:
            return "Problema";
          default:
            return stageName;
        }
      } else if (stageName === "Expedido" && !deliveries && deliveriesFetched) {
        return "Expedição";
      }
    },
    [deliveriesFetched],
  );

  useEffect(() => {
    const determinedStage = () => {
      if (!proposalData) return;

      let stageName = formatStageName(proposalData.status);

      if (!stageName) return;
      if (stageName === "Aprovado") {
        setStage("Aprovado");
        return;
      }

      if (
        stageName === "Em Produção" &&
        !getCurrentProduction({
          production: productionData,
          isNew: proposalData?.newProposal,
        })?.startProductionDate &&
        productionFetched
      ) {
        setStage("Aprovado");
        return;
      }

      if (
        stageName === "Em Produção" &&
        getCurrentProduction({
          production: productionData,
          isNew: proposalData?.newProposal,
        })?.manufacturedDate
      ) {
        setStage("Manufaturado");
        return;
      }

      if (stageName !== "Expedido" && productionFetched) {
        setStage(stageName);
        return;
      }

      if (deliveriesFetched) {
        stageName = checkCurrentStep(
          stageName,
          getCurrentDelivery({
            delivery: deliveriesData,
            isNew: proposalData?.newProposal,
          })?.delivery,
        );

        setStage(stageName);
      }

      if (
        stageName === "Expedido" &&
        proposalData?.carrier === "Retirar Na Fábrica"
      ) {
        setStage("Finalizado");
      }
    };
    determinedStage();
  }, [
    proposalData,
    deliveriesData,
    deliveriesFetched,
    productionData,
    productionFetched,
    checkCurrentStep,
    getCurrentProduction,
    currentProductionSelected,
    formatStageName,
    getCurrentDelivery,
  ]);

  const getForecastDelivery = (): string | null => {
    if (isNewProposal) {
      return (
        getCurrentProduction({
          production: productionData,
          isNew: proposalData?.newProposal,
        })?.expectedDeliveryDate ?? null
      );
    }

    const hasDelivery = proposalData?.hasDelivery;
    const currentStage = stage;
    const isFactoryRetreat = proposalData?.hasDelivery === false;
    if (!hasDelivery || isFactoryRetreat) return null;
    if (currentStage === "Entregue") return null;
    if (proposalData?.carrier?.toLowerCase().includes("jadlog"))
      return proposalData?.deliveryForecastDate;
    return (
      getCurrentDelivery({
        delivery: deliveriesData,
        isNew: proposalData?.newProposal,
      })?.delivery?.deliveryForecastDate ??
      proposalData?.deliveryForecastDate ??
      null
    );
  };

  return {
    stage,
    status: proposalData?.status,
    dhAprovado: proposalData?.approvedDate,
    endereco: {
      rua: proposalData?.deliveryAddress?.street,
      bairro: proposalData?.deliveryAddress.neighborhood,
      uf: proposalData?.deliveryAddress.state,
      cidade: proposalData?.deliveryAddress.city,
      cep: proposalData?.deliveryAddress.zipcode,
      numero: proposalData?.deliveryAddress.number,
      complemento: proposalData?.deliveryAddress.complement,
    },
    dhExpedicao: proposalData?.shipmentDate,
    client: proposalData?.customer,
    getForecastDelivery,
  };
};

export default useInitialInformation;
