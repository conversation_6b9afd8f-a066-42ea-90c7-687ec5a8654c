import { NextRequest, NextResponse } from "next/server";
import { blockedResponse } from "./middleware/security/bloqued-response";
import { isCurlUserAgent } from "./middleware/security/user-agent-validator";

export function middleware(req: NextRequest) {
  if (isCurlUserAgent(req)) {
    return blockedResponse();
  }
  const { pathname } = req.nextUrl;
  const token = req.cookies.get("tracker_admin_token");

  if (pathname.startsWith("/admin/") && !token) {
    const url = req.nextUrl.clone();
    url.pathname = "/admin";
    return NextResponse.redirect(url);
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)",
  ],
};
