.carousel-container {
    position: relative;
    width: 100%;
    overflow: hidden;
  }
  
  .carousel-slide {
    display: flex;
    transition: transform 0.3s ease-in-out;
  }
  
  .carousel-item {
    min-width: 100%;
    box-sizing: border-box;
  }

.arrowAnimatedLeft {
    animation: 0.6s linear 0s infinite alternate arrowLeft;
}

.arrowAnimatedRight {
    animation: 0.6s linear 0s infinite alternate arrowRight;
}

@keyframes arrowLeft {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-8px);
    }
}

@keyframes arrowRight {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(8px);
    }
}