"use client";

import { ElementType } from "react";
import useTimeline from "../../../../hooks/inititalInformation/progressTimeline/useTimeline";

const alturaTimeline = "100%";
const larguraTimeline = "24px";
const larguraCentroTimeline = "8px";
const tamanhoCirculoItem = 60;
const tamanhoCirculoVerdeItem = "45px";
const tamanhoIcone = "24px";
const tamanhoText = "14px";

const TimelineMobile = () => {
  const {
    progress,
    isAnimationActive,
    isActive,
    isActiveCicle,
    isActiveIcon,
    dates,
    ComponentsCircle,
  } = useTimeline();

  const renderComponentStage = (
    stage: string,
    Icon: ElementType,
    name: string,
  ) => {
    return (
      <div className={`flex  w-full items-center`}>
        <div
          style={{
            width: `${tamanhoCirculoItem}px`,
            height: `${tamanhoCirculoItem}px`,
          }}
          className={`absolute flex justify-center items-center w-[${tamanhoCirculoItem}] h-[${tamanhoCirculoItem}] rounded-full bg-bgProgressTimeline left-[-20px]`}
        >
          <span
            style={{
              width: `${tamanhoCirculoVerdeItem}`,
              height: `${tamanhoCirculoVerdeItem}`,
            }}
            className={`flex justify-center items-center ${isActiveCicle(stage)} rounded-full z-30`}
          >
            <Icon
              className={`w-[${tamanhoIcone}] h-[${tamanhoIcone}] ${isActiveIcon(stage)}  bg-transparent`}
            />
          </span>
        </div>

        <div
          style={{ marginLeft: `-10px` }}
          className={`w-full flex-col flex justify-center `}
        >
          <p
            className={`text-[${tamanhoText}] ${
              isActive("Aprovado") ? "font-semibold" : ""
            } text-center text-textColorPrimary`}
          >
            {name.toUpperCase()}
          </p>
          <hr className="w-full h-[3px] bg-bgProgressTimeline" />
          <p className="text-xs mt-[2px] text-center text-textColorPrimary">
            {dates[stage as keyof typeof dates] ? (
              <>{dates[stage as keyof typeof dates]}</>
            ) : (
              `ㅤ`
            )}
          </p>
        </div>
      </div>
    );
  };

  const calculateProgress = progress ? progress + progress * 0.05 : 0;

  return (
    <main className="w-full  h-full flex items-center justify-center mb-2">
      <div
        className={`w-[90%] tl320:w-[85%] pb-4 tl480:w-[65%] h-[${alturaTimeline}]`}
      >
        <div className="relative w-full h-full flex justify-between">
          <div
            className={`h-full w-[${larguraTimeline}] max-h-[90%] mt-[5.5%] bg-bgProgressTimeline `}
          ></div>
          <div className="relative left-[-15px] max-h-[90%]  w-6 mt-[10%] overflow-hidden">
            <div
              style={{
                height: `${calculateProgress}%`,
                backgroundColor: "var(--bg-green-progress-timeline)",
                width: `${larguraCentroTimeline}`,
              }}
              className={`absolute   rounded-lg   z-20 ${isAnimationActive ? "barTransitionVertical" : ""}`}
            ></div>
          </div>

          <div className="h-full  w-full flex flex-col justify-between">
            {ComponentsCircle.map(({ stage, name, Icon }) => (
              <div
                key={stage + name}
                className="justify-between flex w-full items-center"
              >
                {renderComponentStage(stage, Icon, name)}
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
};

export default TimelineMobile;
