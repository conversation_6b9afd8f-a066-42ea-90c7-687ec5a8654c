"use server";

import {
  ErrorData,
  handleGlobalErrors,
} from "@/lib/handleErrors/handleRequestErros";
import { apiInstance } from "@/services/api/apiInstance";
import { IResponse } from "@/types/utils";
import { verifyExpiration } from "./decodedToken";
import { saveCookies } from "@/lib/cookies/saveCookies";
import { dataAccess } from "@/types/auth";

export const login = async (
  cpfCnpj: string,
  proposalCode: string,
  id: string,
): Promise<IResponse<boolean | ErrorData>> => {
  const requestBody = {
    cnpjCpf: cpfCnpj,
    proposal: proposalCode,
  };
  try {
    const { data, status } = await apiInstance.post<dataAccess>(
      "/auth/login",
      requestBody,
      {
        headers: {
          "X-Client-ID": id,
        },
        timeout: 30000,
      },
    );
    const { access_token, refresh_token } = data;
    const expToken = verifyExpiration(access_token);
    const expRefreshToken = verifyExpiration(refresh_token);

    if (expToken && expRefreshToken) {
      saveCookies("access_token", access_token, expToken);
      saveCookies("refresh_token", refresh_token, expRefreshToken);
    }

    return {
      success: true,
      data: true,
      status: status,
    };
  } catch (error: unknown) {
    return handleGlobalErrors(error);
  }
};
