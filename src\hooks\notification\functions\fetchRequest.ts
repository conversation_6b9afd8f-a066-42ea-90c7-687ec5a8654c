"use server";

export interface IEmail {
  email: boolean;
}

import {
  ErrorData,
  handleGlobalErrors,
} from "@/lib/handleErrors/handleRequestErros";
import apiInstance from "@/services/api/apiInstance";
import { IResponse } from "@/types/utils";

const fetchEmail = async (
  id: string,
): Promise<IResponse<IEmail | ErrorData>> => {
  try {
    const { data, status } = await apiInstance.get<IEmail>(
      "/notification-options/find",
      {
        headers: {
          "X-Client-ID": id,
        },
      },
    );
    return { success: true, data: data, status: status };
  } catch (error) {
    return handleGlobalErrors(error);
  }
};

export default fetchEmail;
