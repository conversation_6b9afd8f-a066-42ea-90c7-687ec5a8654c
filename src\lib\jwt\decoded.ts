interface JWTPayload {
  [key: string]: string | number | boolean | null | object;
}

export const decodeJWT = (token: string | null): JWTPayload | null => {
  try {
    if (!token) return null;
    const base64Url = token.split(".")[1];
    const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
    const jsonPayload = Buffer.from(base64, "base64").toString("utf8");
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error("O JWT fornecido é inválido", error);
    return null;
  }
};
