"use client";
import { useTheme } from "@/contexts/theme/themeContext";
import Image from "next/image";
import PormadeBranco from "../../../assets/images/logo_branco.png";
import PormadeLogo from "../../../assets/images/logo_preta.png";

export const Logo = () => {
  const { theme } = useTheme();

  return (
    <div className="relative h-14 w-[12rem]">
      <Image
        src={theme === "light" || !theme ? PormadeLogo : PormadeBranco}
        alt="Logo Pormade"
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        style={{ objectFit: "contain" }}
        priority={true}
      />
    </div>
  );
};
