export const Rodape = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="393"
    height="331"
    viewBox="0 0 393 331"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M246.5 2.00045C240.5 2.80045 235.667 9.00045 234 12.0005V35.0005L350.5 174.5L350 165V147L351.5 142L355.5 137L360.5 134.5L377 133.5H390.5L258.5 1.50045C257 1.33379 252.5 1.20045 246.5 2.00045Z"
      fill="#D9D9D9"
      stroke="black"
    />
    <path
      d="M391.5 210.5L390 264L385 258.5L379 260L378.5 210.5H391.5Z"
      fill="black"
      stroke="black"
    />
    <path
      d="M349.5 329L391.5 328L392 273L390.5 265.5L388 261.5L385 259L379 260V210.5H391.5L392 209V138L391 134L389.5 133L365 134L361 134.5L355.5 137L353 139.5L351 142.5L349.5 148.5V160L350.5 173.5L353 178L353.5 182V184.5L352.5 186L350.5 187.5L349.5 188.5V329Z"
      fill="#686363"
      stroke="black"
    />
    <path
      d="M234 141L349.5 328.5V187.5L234 43V141Z"
      fill="#D9D9D9"
      stroke="black"
    />
    <g filter="url(#filter0_i_0_1)">
      <path
        d="M353 177.5L350.5 173.5L234 35V42.5L349 187H351L352 186.5L353 185.5L354 184V182.5V180.5L353 177.5Z"
        fill="#D9D9D9"
      />
    </g>
    <path
      d="M353 177.5L350.5 173.5L234 35V42.5L349 187H351L352 186.5L353 185.5L354 184V182.5V180.5L353 177.5Z"
      stroke="black"
    />
    <path
      d="M1 33.4999V1.49992H248.644C249.328 1.4227 250.011 1.41853 250.687 1.49992H248.644C242.609 2.18164 236.507 8.55659 234 11.9999L233.5 33.4999H1Z"
      fill="#D9D9D9"
    />
    <path
      d="M251 1.49992H250.687M250.687 1.49992H1V33.4999H233.5L234 11.9999C236.791 8.16658 244.037 0.699918 250.687 1.49992Z"
      stroke="black"
    />
    <g filter="url(#filter1_i_0_1)">
      <path d="M1 43V33.5H233V35L234 43H1Z" fill="#D9D9D9" />
    </g>
    <path d="M1 43V33.5H233V35L234 43H1Z" stroke="black" />
    <path
      d="M0.5 140.5V43.5L234 43V141.5L0.5 140.5Z"
      fill="#D9D9D9"
      stroke="black"
    />
    <defs>
      <filter
        id="filter0_i_0_1"
        x="233.5"
        y="33.6289"
        width="121"
        height="157.871"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="8" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"
        />
        <feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_1" />
      </filter>
      <filter
        id="filter1_i_0_1"
        x="0.5"
        y="33"
        width="234.066"
        height="14.5"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="8" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"
        />
        <feBlend mode="normal" in2="shape" result="effect1_innerShadow_0_1" />
      </filter>
    </defs>
  </svg>
);
