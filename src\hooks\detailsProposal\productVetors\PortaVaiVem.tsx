export const PortaVaiVem = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="358"
    height="627"
    viewBox="0 0 358 627"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="35"
      y="33"
      width="288"
      height="593"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="289"
      y="325.323"
      width="12"
      height="13.7079"
      rx="0.5"
      fill="black"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M290 327.016C290 326.739 290.224 326.516 290.5 326.516H299.5C299.776 326.516 300 326.739 300 327.016V337.935C300 338.212 299.776 338.435 299.5 338.435H290.5C290.224 338.435 290 338.212 290 337.935V327.016Z"
      fill="#202020"
      stroke="black"
      strokeWidth="2"
    />
    <g filter="url(#filter0_i_767_177)">
      <path
        d="M290 307.914C290 305.705 291.791 303.914 294 303.914H296C298.209 303.914 300 305.705 300 307.914V308.945C300 311.154 298.209 312.945 296 312.945H294C291.791 312.945 290 311.154 290 308.945V307.914Z"
        fill="#474747"
      />
    </g>
    <rect
      x="289"
      y="299.9"
      width="12"
      height="18.0609"
      fill="#010101"
      stroke="black"
      strokeWidth="2"
    />
    <g filter="url(#filter1_d_767_177)">
      <path
        d="M258 304.917H295.986C298.203 304.917 300 306.714 300 308.931V308.931C300 311.147 298.203 312.944 295.986 312.944H258V304.917Z"
        fill="#222121"
      />
      <path
        d="M258.5 305.417H295.986C297.927 305.417 299.5 306.99 299.5 308.931C299.5 310.871 297.927 312.444 295.986 312.444H258.5V305.417Z"
        stroke="white"
        strokeOpacity="0.4"
      />
    </g>
    <rect x="323" y="31.832" width="35" height="595.168" fill="black" />
    <rect y="30" width="35" height="597" fill="black" />
    <rect x="323" y="34.7256" width="3.04348" height="592.274" fill="black" />
    <g filter="url(#filter2_i_767_177)">
      <rect
        x="0.5"
        y="0.5"
        width="357"
        height="33.7262"
        fill="black"
        stroke="black"
      />
    </g>
    <rect x="35" y="173" width="7" height="32" fill="black" />
    <rect x="37" y="204" width="4" height="2" fill="#030303" />
    <rect x="37" y="204" width="4" height="2" fill="#030303" />
    <rect x="37" y="172" width="4" height="2" fill="#030303" />
    <rect x="37" y="172" width="4" height="2" fill="#030303" />
    <rect x="35" y="533" width="7" height="32" fill="black" />
    <rect x="37" y="564" width="4" height="2" fill="#030303" />
    <rect x="37" y="564" width="4" height="2" fill="#030303" />
    <rect x="37" y="532" width="4" height="2" fill="#030303" />
    <rect x="37" y="532" width="4" height="2" fill="#030303" />
    <rect x="35" y="86" width="7" height="32" fill="black" />
    <rect x="37" y="117" width="4" height="2" fill="#030303" />
    <rect x="37" y="117" width="4" height="2" fill="#030303" />
    <rect x="37" y="85" width="4" height="2" fill="#030303" />
    <rect x="37" y="85" width="4" height="2" fill="#030303" />
    <defs>
      <filter
        id="filter0_i_767_177"
        x="290"
        y="303.914"
        width="10"
        height="13.0303"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_767_177"
        />
      </filter>
      <filter
        id="filter1_d_767_177"
        x="254"
        y="304.917"
        width="50"
        height="16.0273"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_767_177"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_767_177"
          result="shape"
        />
      </filter>
      <filter
        id="filter2_i_767_177"
        x="0"
        y="0"
        width="358"
        height="38.7266"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_767_177"
        />
      </filter>
    </defs>
  </svg>
);
