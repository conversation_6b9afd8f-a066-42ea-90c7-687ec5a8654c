export const formatDescription = (
  description: string,
  isPartner: boolean | undefined,
  doubleDeliveryFretefy: boolean | undefined,
  doubleDeliverySsw: boolean | undefined,
): string | null => {
  const isPartnerOnly =
    isPartner && !doubleDeliveryFretefy && !doubleDeliverySsw;

  const entregueAoTransportadorParceiro = doubleDeliverySsw
    ? "Entregue ao transportador parceiro"
    : "Entregue";

  const descriptionEvent = isPartnerOnly
    ? "Entregue ao transportador parceiro"
    : entregueAoTransportadorParceiro;
  const descriptionMap: { [key: string]: string } = {
    coleta: "Coletado",
    "registro de coleta": "Coletado",
    "foto entrega": descriptionEvent,
  };

  const cleanDescription = description?.replace(/\(.*?\)/g, "").trim();

  const ignoredDescriptions = ["checklist concluido", "repetidos"];
  if (ignoredDescriptions.includes(cleanDescription?.toLowerCase())) {
    return null;
  }

  return descriptionMap[cleanDescription?.toLowerCase()] || cleanDescription;
};
