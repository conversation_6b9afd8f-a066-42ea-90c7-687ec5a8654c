import React, { useEffect } from "react";
import ReactDOM from "react-dom";

interface ToastFetchProps {
  message: string | null;
}

export const ToastLoadingFetch: React.FC<ToastFetchProps> = ({ message }) => {
  useEffect(() => {
    const rootElement = document.getElementById("toast-fetch-root");
    if (!rootElement) {
      const toastRoot = document.createElement("div");
      document.body.appendChild(toastRoot);
    }
  }, []);

  return ReactDOM.createPortal(
    <div
      id="toast-fetch"
      className={`sm:w-[280px]  h-[10px] sm:h-[70px]  border border-green-700 backdrop-blur-md shadow-lg rounded-lg fixed top-[6rem] sm:top-[4.5rem] right-5 z-10 flex items-center p-8 sm:p-4 transition-transform duration-300 transform  sm:translate-x-0`}
    >
      <div className="w-1/4 flex justify-center items-center">
        <div className="w-8 h-8 border-4 border-green-700 border-t-transparent border-t-4 border-t-green-700 rounded-full animate-spin"></div>
      </div>
      <div className="w-3/4 h-full flex flex-col justify-center items-start pl-4">
        <p className="text-sm text-textColorPrimary font-semibold">
          {message ?? "Carregando..."}
        </p>
      </div>
    </div>,
    document.getElementById("toast-fetch-root")!,
  );
};
