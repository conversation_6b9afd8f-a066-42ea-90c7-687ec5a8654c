"use server";

import { cookies } from "next/headers";

export const getCookies = async (name: string) => {
  try {
    const cookieStore = await cookies();
    const cookie = cookieStore.get(name);
    if (cookie) {
      return cookie.value;
    } else {
      return null;
    }
  } catch (error) {
    return null;
  }
};

export const saveCookies = async (
  name: string,
  value: string,
  time: number,
) => {
  const exp = new Date(time * 1000);
  const cookieStore = await cookies();
  return cookieStore.set(name, value, {
    expires: exp,
    sameSite: "strict",
    secure: true,
    httpOnly: true,
  });
};

export const removeCookies = async (name: string): Promise<boolean> => {
  try {
    const cookieStore = await cookies();
    cookieStore.delete(name);
    return true;
  } catch (error) {
    return false;
  }
};
