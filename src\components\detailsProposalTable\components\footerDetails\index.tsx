"use client";
import { useDataContext } from "@/contexts/data/dataContext";
import { formatCurrency } from "@/hooks/detailsProposal/functions/formatCurrency";

const FooterDetails = () => {
  const { proposalData } = useDataContext();

  if (!proposalData) return <FooterDetailsLoading />;

  const { totalValue, freightValue, carrier } = proposalData;
  const isPickup = carrier?.toLowerCase() === "retirar na fábrica";
  const freight =
    freightValue !== "0.000"
      ? formatCurrency(parseFloat(freightValue || "0"))
      : "Gr<PERSON><PERSON>";

  return (
    <div className="mb-4 flex w-full sm:w-[80%] text-textColorPrimary">
      <div className="flex w-full flex-col gap-1">
        <FooterDetailItem
          label="Valor do Pedido:"
          value={formatCurrency(parseFloat(totalValue || "0"))}
        />
        {!isPickup && <FooterDetailItem label="Frete:" value={freight} />}
        <FooterDetailItem
          label={isPickup ? "Local de Retirada:" : "Transportadora:"}
          value={isPickup ? "Retirar na Fábrica" : carrier}
        />
      </div>
    </div>
  );
};

const FooterDetailItem = ({
  label,
  value,
}: {
  label: string;
  value: string;
}) => (
  <article className="flex w-full justify-between">
    <p className="text-sm font-medium md:text-sm lg:text-lg xl:text-lg">
      {label}
    </p>
    <p className="text-sm md:text-sm lg:text-lg xl:text-lg text-end">{value}</p>
  </article>
);

const FooterDetailsLoading = () => (
  <div className="mb-4 h-[110px] flex w-full sm:w-[80%] text-textColorPrimary">
    <div className="flex w-full flex-col gap-1">
      {[...Array(3)].map((_, i) => (
        <article key={i} className="flex w-full justify-between">
          <p className="text-sm h-[30px] animate-pulseLoading w-[20%] font-medium md:text-sm lg:text-lg xl:text-lg"></p>
          <p className="text-sm h-[30px] animate-pulseLoading w-[20%] md:text-sm lg:text-lg xl:text-lg text-end"></p>
        </article>
      ))}
    </div>
  </div>
);

export default FooterDetails;
