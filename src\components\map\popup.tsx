import { useMap } from "@vis.gl/react-google-maps";
import React, { useEffect, useRef, useState } from "react";

interface CustomPopupOverlayProps {
  position: google.maps.LatLngLiteral;
  content: string | React.ReactNode;
  minZoom?: number;
  onClick?: () => void;
}

const MapPopupOverlay: React.FC<CustomPopupOverlayProps> = ({
  position,
  content,
  minZoom = 10,
  onClick,
}) => {
  const mapInstance = useMap();

  const mapOverlayRef = useRef<google.maps.OverlayView | null>(null);
  const [isMapPopupVisible, setMapPopupVisible] = useState(false);

  useEffect(() => {
    if (!mapInstance) return;
    const handleZoomChange = () => {
      const currentMapZoomLevel = mapInstance.getZoom();
      setMapPopupVisible(
        currentMapZoomLevel !== undefined && currentMapZoomLevel >= minZoom,
      );
    };

    mapInstance.addListener("zoom_changed", handleZoomChange);
    handleZoomChange();

    return () => {
      google.maps.event.clearListeners(mapInstance, "zoom_changed");
    };
  }, [mapInstance, minZoom, content]);

  useEffect(() => {
    if (
      !mapInstance ||
      !isMapPopupVisible ||
      typeof mapInstance.getZoom !== "function"
    )
      return;
    class LocationTooltip extends google.maps.OverlayView {
      popupPosition: google.maps.LatLng;
      popupContainer: HTMLDivElement;

      constructor(pos: google.maps.LatLng, innerContent: HTMLElement) {
        super();

        this.popupPosition = pos;
        innerContent.classList.add(
          ...[
            "bg-white",
            "background-black",
            "rounded-[6px]",
            "p-[12px]",
            "text-[14px]",
            "!bg-[var(--bg-primary-white)]",
            "text-[var(--text-color-primary)]",
            "shadow-xl",
          ],
        );

        innerContent.id = `map-popup-content-${typeof content === "string" ? content.toLowerCase() : "unknown"}-${Date.now()}`;

        const bubbleAnchor = document.createElement("div");
        bubbleAnchor.classList.add("relative");
        bubbleAnchor.appendChild(innerContent);
        this.popupContainer = document.createElement("div");
        this.popupContainer.onclick = () => {
          if (onClick) onClick();
        };

        this.popupContainer.classList.add(
          ...["absolute", "w-auto", "h-auto", "overflow-visible"],
        );
        this.popupContainer.appendChild(bubbleAnchor);
        LocationTooltip.preventMapHitsAndGesturesFrom(this.popupContainer);
      }

      onAdd() {
        this.getPanes()?.floatPane.appendChild(this.popupContainer);
      }

      onRemove() {
        if (this.popupContainer.parentElement) {
          this.popupContainer.parentElement.removeChild(this.popupContainer);
        }
      }

      draw() {
        const divPosition = this.getProjection().fromLatLngToDivPixel(
          this.popupPosition,
        );

        if (!divPosition) return;
        const display =
          Math.abs(divPosition.x) < 4000 && Math.abs(divPosition.y) < 4000
            ? "block"
            : "none";
        if (display === "block") {
          this.popupContainer.style.left = `${divPosition.x}px`;
          this.popupContainer.style.top = `${divPosition.y}px`;
        }
        if (this.popupContainer.style.display !== display) {
          this.popupContainer.style.display = display;
        }
      }
    }

    const popupContentElement = document.createElement("div");

    if (typeof content === "string") {
      popupContentElement.innerHTML = content;
    } else {
      popupContentElement.innerHTML = String(content);
    }

    let coordinates;
    try {
      coordinates = new google.maps.LatLng(position.lat, position.lng);
    } catch (error) {
      console.error(
        "Falha ao criar o objeto LatLng para a posição do pop-up:",
        error,
      );
    }

    if (coordinates) {
      const popup = new LocationTooltip(coordinates, popupContentElement);

      if (mapOverlayRef.current) {
        return;
      }

      mapOverlayRef.current = popup;
      popup.setMap(mapInstance);

      return () => {
        popup.setMap(null);
        mapOverlayRef.current = null;
      };
    }
  }, [mapInstance, position, content, isMapPopupVisible, onClick]);

  return null;
};

export default MapPopupOverlay;
