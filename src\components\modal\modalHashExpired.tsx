"use client";
import { useEffect } from "react";
import ReactModal from "react-modal";
import Image from "next/image";

interface ModalExpiredProps {
  isOpen: boolean;
  onRequestClose: () => void;
  style?: React.CSSProperties;
}

const ModalExpired = ({ isOpen, onRequestClose, style }: ModalExpiredProps) => {
  useEffect(() => {
    if (typeof window !== "undefined") {
      ReactModal.setAppElement("body");
    }
  }, []);

  const handleFocus = () => {
    const input = document.getElementById("orderId");
    if (input) {
      input.focus();
    }
  };

  return (
    <ReactModal
      isOpen={isOpen}
      ariaHideApp={false}
      overlayClassName="flex justify-center items-center bg-black bg-opacity-70 fixed top-0 left-0 right-0 bottom-0 z-50"
      className="text-textColorPrimary w-[500px] h-[500px] outline-none border-none bg-bgPrimaryWhite rounded-sm z-40 m-auto"
      style={{ overlay: style }}
    >
      <div className="items-center justify-center h-[100%] flex flex-col">
        <div className="flex justify-center text-center">
          <h1 className="text-[24px] font-bold">
            Seu link de rastreamento expirou
          </h1>
        </div>

        <div className="flex justify-center mt-4">
          <Image
            src="/assets/images/expired.png"
            alt="Imagem"
            width={200}
            height={200}
          />
        </div>

        <div className="flex justify-center mt-4">
          <p className="text-center text-[20px] text-textColorPrimary">
            Clique no botão abaixo para acessar as informações
          </p>
        </div>

        <div className="flex justify-center mt-5">
          <button
            className="bg-PormadeGreen text-textColorPrimary py-2  h-[44px] px-4 rounded hover:bg-PormadeGreen  shadow-md "
            onClick={() => {
              onRequestClose();
              handleFocus();
            }}
          >
            Acessar novamente
          </button>
        </div>
      </div>
    </ReactModal>
  );
};

export default ModalExpired;
