export const Bandeira = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="358"
    height="789"
    viewBox="0 0 358 789"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="323" y="34" width="35" height="755" fill="black" />
    <rect y="34" width="35" height="755" fill="black" />
    <path d="M323 196.226H326.043V788.499H323V196.226Z" fill="black" />
    <g filter="url(#filter0_i_679_150)">
      <rect
        x="0.5"
        y="0.5"
        width="357"
        height="33.7262"
        fill="black"
        stroke="black"
      />
    </g>
    <rect width="358" height="190" fill="black" />
    <defs>
      <filter
        id="filter0_i_679_150"
        x="0"
        y="0"
        width="358"
        height="38.7266"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_679_150"
        />
      </filter>
    </defs>
  </svg>
);
