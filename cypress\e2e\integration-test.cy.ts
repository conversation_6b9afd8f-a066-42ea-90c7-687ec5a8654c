describe("Teste de Integração - Fluxo Completo de Acesso", () => {
  let testData: Array<{ proposalCode: string; cpfCnpj: string }> = [];

  before(() => {
    // Limpa o arquivo de erros anterior
    const failureLogFile = `cypress/fixtures/failed-logins.json`;
    cy.writeFile(failureLogFile, []);

    // Lê os dados do arquivo de teste com formato de aspas duplas
    cy.readQuotedTestDataFile("cypress/fixtures/test-data.txt").then((data) => {
      testData = data;

      if (testData.length === 0) {
        throw new Error(
          'Nenhum dado de teste encontrado. Adicione dados no arquivo cypress/fixtures/test-data.txt no formato "numero-versao""cpf/cnpj"',
        );
      }

      // Log da contagem total de itens que serão testados
      cy.log(`📊 TOTAL DE ITENS PARA TESTE: ${testData.length}`);
      cy.log(`🚀 Iniciando bateria de testes de integração...`);
    });
  });

  beforeEach(() => {
    // Configurações antes de cada teste
    cy.viewport(1280, 720);

    // // Intercepta possíveis chamadas de API para evitar erros de rede
    // cy.intercept("POST", "**/api/**", { statusCode: 200 }).as("apiCall");
  });

  it("Deve executar o fluxo completo para todos os dados de teste", () => {
    // Verifica se há dados para testar
    expect(testData.length).to.be.greaterThan(0);

    // Log inicial com contagem
    cy.log(`🎯 INICIANDO EXECUÇÃO DE ${testData.length} TESTES`);

    // Executa o teste para cada conjunto de dados
    testData.forEach((data, index) => {
      const progress = `[${index + 1}/${testData.length}]`;
      const percentage = Math.round(((index + 1) / testData.length) * 100);
      
      cy.log(
        `${progress} 🔄 Executando teste ${index + 1}/${testData.length} (${percentage}%): ${data.proposalCode} | ${data.cpfCnpj}`,
      );

      // Verifica se os dados são válidos
      if (!data.proposalCode || !data.cpfCnpj) {
        cy.log(`${progress} ⚠️ Dados inválidos ignorados: ${JSON.stringify(data)}`);
        return;
      }

      // Executa o fluxo de teste (sem exigir sucesso no login)
      executeTestFlowWithValidation(data.proposalCode, data.cpfCnpj, index + 1, testData.length);
    });

    // Log final
    cy.log(`✅ CONCLUÍDA EXECUÇÃO DE TODOS OS ${testData.length} TESTES`);
  });

  it("Deve executar teste com dados de exemplo", () => {
    // Teste usando dados de exemplo para demonstração
    cy.readQuotedTestDataFile("cypress/fixtures/test-data-example.txt").then(
      (exampleData) => {
        if (exampleData.length > 0) {
          const data = exampleData[0];
          executeTestFlow(data.proposalCode, data.cpfCnpj, 1);
        }
      },
    );
  });

  /**
   * Executa o fluxo completo de teste para um conjunto de dados (versão original)
   */
  function executeTestFlow(
    proposalCode: string,
    cpfCnpj: string,
    testNumber: number,
  ) {
    cy.log(
      `Iniciando fluxo de teste ${testNumber} com dados: ${proposalCode} | ${cpfCnpj}`,
    );

    // Passo 1: Realizar login
    cy.loginWithData(proposalCode, cpfCnpj);

    // Passo 2: Aguardar e fazer scroll na página do dashboard
    cy.waitAndScroll();

    // Passo 3: Realizar logout
    cy.performLogout();

    // Aguarda um pouco antes do próximo teste
    cy.wait(100);
  }

  /**
   * Executa o fluxo de teste com validação flexível (não exige sucesso no login)
   */
  function executeTestFlowWithValidation(
    proposalCode: string,
    cpfCnpj: string,
    testNumber: number,
    totalTests?: number,
  ) {
    const progress = totalTests ? `[${testNumber}/${totalTests}]` : `${testNumber}`;
    
    cy.log(
      `${progress} Iniciando fluxo de teste ${testNumber} com dados: ${proposalCode} | ${cpfCnpj}`,
    );

    // Passo 1: Tentar login - sempre continua independente do resultado
    cy.loginWithData(proposalCode, cpfCnpj).then(() => {
      // Passo 2: Verificar se chegou ao dashboard
      cy.url().then((currentUrl) => {
        if (currentUrl.includes("/dashboard")) {
          cy.log(
            `${progress} ✅ Login bem-sucedido - executando fluxo completo no DASHBOARD`,
          );

          // Aguardar e fazer scroll APENAS na página do dashboard
          cy.log(`${progress} 🔄 Executando scroll no dashboard...`);
          cy.waitAndScroll();

          // Realizar logout
          cy.log(`${progress} 🚪 Realizando logout...`);
          cy.performLogout();
        } else {
          cy.log(
            `${progress} ⚠️ Login não redirecionou para dashboard (URL: ${currentUrl}) - SEM SCROLL`,
          );
        }
      });

      // Aguarda um pouco antes do próximo teste
      cy.wait(500);

      // Log para confirmar que o teste foi finalizado
      cy.log(`${progress} 🔚 Teste ${testNumber} finalizado - prosseguindo para próximo`);
    });
  }
});
