export const Esquadria = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="496"
    height="465"
    viewBox="0 0 496 465"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g filter="url(#filter0_d_779_163)">
      <rect
        x="0.5"
        y="0.5"
        width="487"
        height="456"
        fill="#D9D9D9"
        stroke="black"
      />
      <g filter="url(#filter1_i_779_163)">
        <rect x="248" y="19" width="225" height="420" fill="#D9D9D9" />
      </g>
      <rect x="248.5" y="19.5" width="224" height="419" stroke="black" />
      <circle cx="252.5" cy="80.5" r="2.5" fill="#D9D9D9" />
      <g filter="url(#filter2_i_779_163)">
        <rect x="248" y="32" width="206" height="393" fill="#D9D9D9" />
      </g>
      <rect x="248.5" y="32.5" width="205" height="392" stroke="black" />
      <rect
        x="458.5"
        y="195.5"
        width="8"
        height="66"
        rx="4"
        fill="black"
        stroke="black"
      />
      <circle cx="462.5" cy="256.5" r="2.5" fill="#D9D9D9" />
      <circle cx="462.5" cy="201.5" r="2.5" fill="#D9D9D9" />
      <g filter="url(#filter3_i_779_163)">
        <rect
          x="14.5"
          y="19.5"
          width="233"
          height="419"
          fill="#D9D9D9"
          stroke="black"
        />
        <g filter="url(#filter4_i_779_163)">
          <rect x="32" y="32" width="197" height="393" fill="#D9D9D9" />
        </g>
        <rect x="32.5" y="32.5" width="196" height="392" stroke="black" />
        <rect
          x="19.5"
          y="195.5"
          width="8"
          height="66"
          rx="4"
          fill="black"
          stroke="black"
        />
        <circle cx="23.5" cy="256.5" r="2.5" fill="#D9D9D9" />
        <circle cx="23.5" cy="201.5" r="2.5" fill="#D9D9D9" />
      </g>
    </g>
    <defs>
      <filter
        id="filter0_d_779_163"
        x="0"
        y="0"
        width="496"
        height="465"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="4" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_779_163"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_779_163"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_i_779_163"
        x="248"
        y="19"
        width="225"
        height="424"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_779_163"
        />
      </filter>
      <filter
        id="filter2_i_779_163"
        x="218"
        y="32"
        width="236"
        height="418"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="-30" dy="25" />
        <feGaussianBlur stdDeviation="50" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_779_163"
        />
      </filter>
      <filter
        id="filter3_i_779_163"
        x="14"
        y="19"
        width="238"
        height="420"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_779_163"
        />
      </filter>
      <filter
        id="filter4_i_779_163"
        x="32"
        y="32"
        width="227"
        height="418"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="30" dy="25" />
        <feGaussianBlur stdDeviation="50" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_779_163"
        />
      </filter>
    </defs>
  </svg>
);
