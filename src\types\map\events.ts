import { Theme } from "@/contexts/theme/themeContext";
import { MapEvent } from "@vis.gl/react-google-maps";
import { ICoordinates } from "../proposal";

export interface IEventsMapsProps {
  mapRef: React.MutableRefObject<google.maps.Map | null>;
  locations: ICoordinates[];
  theme: Theme | null;
}

export interface IMapsEventHandlers {
  adjustMapZoomToBounds: (map: google.maps.Map) => void;
  setMapFocus: (position: google.maps.LatLngLiteral) => void;
  onTilesLoaded: (eventMap: MapEvent<unknown>) => void;

  getPositionWithName: ({
    name,
  }: {
    name: string;
  }) => google.maps.LatLngLiteral | null;
}
