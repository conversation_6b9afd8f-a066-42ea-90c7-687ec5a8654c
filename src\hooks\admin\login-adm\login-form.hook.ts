import { useState } from "react";
import toast from "react-hot-toast";
import { useLoginMutation } from "./login-mutation.hook";

// interface ILoginForm {
//   user: string;
//   password: string;
// }

export const useLoginForm = () => {
  const [user, setUser] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [showUserLabel, setShowUserLabel] = useState<boolean>(false);
  const [showPasswordLabel, setShowPasswordLabel] = useState<boolean>(false);

  const { login } = useLoginMutation();

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    toast.dismiss();
    toast.loading("Carregando...");

    if (user === "" || password === "") {
      toast.dismiss();
      toast.error("Preencha todos os campos");
      return;
    }

    login({
      user,
      password,
    });

    // if (response?.error) {
    //   toast.dismiss();
    //   toast.error(response?.error);
    //   return;
    // }

    // toast.dismiss();
    // toast.success("Login efetuado com sucesso");
  };

  return {
    user,
    setUser,
    password,
    setPassword,
    showUserLabel,
    setShowUserLabel,
    showPasswordLabel,
    setShowPasswordLabel,
    handleSubmit,
  };
};
