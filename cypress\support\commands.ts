// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Lê dados de um arquivo de texto e retorna como array
       * @param filePath - Caminho para o arquivo de texto
       */
      readTestDataFile(filePath: string): Chainable<string[]>;

      /**
       * Lê dados de um arquivo de texto com formato "valor1""valor2" e retorna como array de objetos
       * @param filePath - Caminho para o arquivo de texto
       */
      readQuotedTestDataFile(
        filePath: string,
      ): Chainable<Array<{ proposalCode: string; cpfCnpj: string }>>;

      /**
       * Realiza login na aplicação com os dados fornecidos
       * @param proposalCode - Código da proposta (numero-versao)
       * @param cpfCnpj - CPF ou CNPJ
       */
      loginWithData(proposalCode: string, cpfCnpj: string): Chainable<void>;

      /**
       * Realiza logout da aplicação
       */
      performLogout(): Chainable<void>;

      /**
       * Aguarda e faz scroll na página (apenas se estiver no dashboard)
       */
      waitAndScroll(): Chainable<void>;
    }
  }
}

// Comando para ler arquivo de dados de teste
Cypress.Commands.add("readTestDataFile", (filePath: string) => {
  return cy.readFile(filePath, "utf8").then((content: string) => {
    return content
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0);
  });
});

// Função auxiliar para extrair dados das aspas duplas
function parseQuotedData(
  line: string,
): { proposalCode: string; cpfCnpj: string } | null {
  // Regex para capturar dois valores entre aspas duplas: "valor1""valor2"
  const regex = /"([^"]+)""([^"]+)"/;
  const match = line.match(regex);

  if (match && match[1] && match[2]) {
    return {
      proposalCode: match[1],
      cpfCnpj: match[2],
    };
  }

  return null;
}

// Comando para ler arquivo de dados de teste com formato de aspas duplas
Cypress.Commands.add("readQuotedTestDataFile", (filePath: string) => {
  return cy.readFile(filePath, "utf8").then((content: string) => {
    const lines = content
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0 && !line.startsWith("#"));

    const parsedData: Array<{ proposalCode: string; cpfCnpj: string }> = [];

    lines.forEach((line) => {
      const data = parseQuotedData(line);
      if (data) {
        parsedData.push(data);
      }
    });

    return parsedData;
  });
});

// Comando para realizar login com dados específicos
Cypress.Commands.add(
  "loginWithData",
  (proposalCode: string, cpfCnpj: string) => {
    cy.log(`Tentando login com: ${proposalCode} | ${cpfCnpj}`);

    // Visita a página de acesso
    cy.visit("/access-page");

    // Aguarda a página carregar
    cy.get("form").should("be.visible");

    // Preenche o campo "Número do Pedido"
    cy.get('input[name="orderId"]').clear().type(proposalCode);

    // Preenche o campo "CPF/CNPJ"
    cy.get('input[name="cnpj"]').clear().type(cpfCnpj);

    // Clica no botão "Rastrear Pedido" - usando first() para evitar erro de múltiplos elementos
    cy.get('button[type="submit"]').contains("Rastrear Pedido").first().click();

    // Aguarda um pouco para ver se há redirecionamento ou erro
    cy.wait(3000);

    // Verifica se houve redirecionamento para dashboard ou se permaneceu na página de acesso
    cy.url().then((currentUrl) => {
      if (currentUrl.includes("/dashboard")) {
        cy.log("✅ Login bem-sucedido - redirecionado para dashboard");
      } else if (currentUrl.includes("/access-page")) {
        cy.log("⚠️ Login falhou - permaneceu na página de acesso");

        // Captura mensagem de toast de erro e salva dados que falharam
        cy.get("body").then(($body) => {
          let errorMessage = "Erro não identificado";

          // Verifica diferentes possíveis seletores de toast de erro
          const possibleSelectors = [
            '[data-testid="toast-error"]',
            ".Toastify__toast--error",
            '[role="alert"]',
            ".toast-error",
            'div[id*="toast"]',
          ];

          // Tenta encontrar mensagem de erro em vários elementos
          possibleSelectors.forEach((selector) => {
            const errorElement = $body.find(selector);
            if (errorElement.length > 0 && errorElement.text().trim()) {
              errorMessage = errorElement.text().trim();
            }
          });

          // Se não encontrou nos seletores específicos, verifica no texto geral
          if (errorMessage === "Erro não identificado") {
            const bodyText = $body.text();
            if (
              bodyText.includes("erro") ||
              bodyText.includes("inválido") ||
              bodyText.includes("não encontrado") ||
              bodyText.includes("Pedido ou CPF/CNPJ não encontrados")
            ) {
              errorMessage = "Erro de validação detectado no body";
            }
          }

          cy.log(`❌ Erro detectado: ${errorMessage}`);

          // Salva os dados que falharam no arquivo de log centralizado
          const failedData = {
            proposalCode,
            cpfCnpj,
            error: errorMessage,
            timestamp: new Date().toISOString(),
            url: currentUrl,
          };

          // Lê o arquivo de erros existente ou cria um novo
          const failureLogFile = `cypress/fixtures/failed-logins.json`;
          cy.task('fileExists', failureLogFile).then((exists) => {
            if (exists) {
              cy.readFile(failureLogFile).then((existingData) => {
                const errors = Array.isArray(existingData) ? existingData : [];
                errors.push(failedData);
                cy.writeFile(failureLogFile, errors).then(() => {
                  cy.log(`✅ Dados de falha salvos em ${failureLogFile}`);
                });
              });
            } else {
              // Se o arquivo não existe, cria um novo
              cy.writeFile(failureLogFile, [failedData]).then(() => {
                cy.log(`✅ Dados de falha salvos em ${failureLogFile}`);
              });
            }
          });
        });
      } else {
        cy.log(`🔄 Redirecionado para: ${currentUrl}`);
      }
    });

    // Sempre retorna sucesso para permitir que o teste continue para o próximo item
    cy.then(() => {
      cy.log(
        "🔄 Comando loginWithData finalizado - prosseguindo para próximo teste",
      );
    });
  },
);

// Comando para realizar logout
Cypress.Commands.add("performLogout", () => {
  // Tenta primeiro a versão desktop (botão visível)
  cy.get("body").then(($body) => {
    const logoutButton = $body.find(".logout-container:visible");

    if (logoutButton.length > 0) {
      // Desktop - clica diretamente no botão de logout (usando first() para evitar erro de múltiplos elementos)
      cy.get(".logout-container").should("be.visible").first().click();
    }

    // e

    // lse {
    //   // Mobile - abre o menu hambúrguer primeiro
    //   cy.get('button[class*="text-3xl"]').first().click();
    //   cy.get(".logout-container").should("be.visible").first().click();
    // }
  });

  // Aguarda redirecionamento para página de acesso
  cy.url().should("include", "/access-page", { timeout: 10000 });
});

// Comando para aguardar e fazer scroll (apenas se estiver no dashboard)
Cypress.Commands.add("waitAndScroll", () => {
  // Verifica se está na página do dashboard antes de fazer scroll
  cy.url().then((currentUrl) => {
    if (currentUrl.includes("/dashboard")) {
      cy.log("✅ Está no dashboard - executando scroll");

      // Aguarda 2 segundos
      cy.wait(500);

      // Faz scroll para baixo
      cy.scrollTo("bottom", { duration: 1000 });

      // Aguarda mais 2 segundos
      cy.wait(500);
    } else {
      cy.log(`⚠️ Não está no dashboard (${currentUrl}) - pulando scroll`);
    }
  });
});

export {};
