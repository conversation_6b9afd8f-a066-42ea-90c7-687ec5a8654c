"use client";

import { useTheme } from "@/contexts/theme/themeContext";
import { <PERSON><PERSON><PERSON>, LuSun } from "react-icons/lu";

export const ThemeButton: React.FC = () => {
  const { toggleTheme, theme } = useTheme();

  return (
    <button
      onClick={() => toggleTheme()}
      className="fixed right-0 w-15 flex justify-center rounded-s-3xl items-center h-5 top-5 p-6 bg-bgPrimaryWhite shadow-md"
    >
      {theme === "light" || !theme ? (
        <LuMoon className="text-3xl text-black" />
      ) : (
        <LuSun className="text-3xl text-white " />
      )}
    </button>
  );
};
