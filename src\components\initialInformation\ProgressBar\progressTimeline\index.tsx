"use client";
import useTimeline from "../../../../hooks/inititalInformation/progressTimeline/useTimeline";
import "./styles.css";

import { Dates, RenderComponentProps } from "@/types/progressBar";
import React from "react";

const RenderComponentCircle: React.FC<RenderComponentProps> = ({
  stage,
  name,
  Icon,
  isActiveText,
  isActiveCicle,
  isActiveIcon,
  dates,
  handleDateStage,
}) => {
  return (
    <div key={stage + name}>
      <div
        className={`bg-bgProgressTimeline  flex justify-center items-center w-[50px] h-[50px] lg:w-[70px] lg:h-[70px] rounded-full shadowClass`}
      >
        <span
          className={`absolute teste ${name === "Previsão de Entrega" ? "w-[150px] top-[-50px]" : "top-[-35px] "} text-center mr-1 ${isActiveText(
            stage,
          )} font-main text-xs sm:text-base`}
        >
          {name}
        </span>
        <span
          className={`${isActiveCicle(
            stage,
          )} flex justify-center teste items-center w-[37px] h-[37px] lg:w-[52px] lg:h-[52px] rounded-full z-30`}
        >
          <Icon
            className={`${isActiveIcon(stage)} w-[24px] h-[24px] lg:w-[31px] lg:h-[31px] bg-transparent z-10 `}
          />

          <div className={`absolute w-[30px] h-[30px] rounded-full`}></div>
        </span>
      </div>
      <div className="absolute w-[100px] teste h-[30px] mt-[10px] ml-[-15px]">
        <p
          className={`block text-[8px] sm:text-base ${handleDateStage(stage)} text-center`}
        >
          {dates[stage as keyof Dates]}
        </p>
      </div>
    </div>
  );
};

const ProgressTimeline = () => {
  const {
    progress,
    isAnimationActive,
    isActiveCicle,
    isActiveIcon,
    isActiveText,
    handleDateStage,
    dates,
    ComponentsCircle,
  } = useTimeline();

  return (
    <div className={` relative w-full flex justify-between`}>
      <div
        className={`absolute left-0 sm:ml-4 bg-transparent w-[94%] sm:w-[96%] h-2/6 sm:h-2/6 lg:h-2/5 top-[34%] sm:top-[33%] lg:top-[30%] z-10 shadowProgress`}
      ></div>
      <div
        className={`absolute left-1 sm:ml-4 bg-bgProgressTimeline  w-[93%] sm:w-[96%] h-2/6 sm:h-2/6 lg:h-2/5 top-[34%] sm:top-[33%] lg:top-[30%] z-10 `}
      ></div>
      <div
        style={{ width: `${progress}%` }}
        className={`absolute left-0 ml-4 mr-5 bg-bgGreenTimeline h-1/5 lg:h-1/5 top-[40.856%] sm:top-[39%] lg:top-[40.654%] rounded-full z-20 ${
          isAnimationActive ? "barTransition" : ""
        }`}
      ></div>

      {ComponentsCircle.map(({ stage, name, Icon }) => (
        <RenderComponentCircle
          key={stage + name}
          stage={stage}
          name={name}
          Icon={Icon}
          isActiveCicle={isActiveCicle}
          isActiveIcon={isActiveIcon}
          isActiveText={isActiveText}
          dates={dates}
          handleDateStage={handleDateStage}
        />
      ))}
    </div>
  );
};

export default ProgressTimeline;
