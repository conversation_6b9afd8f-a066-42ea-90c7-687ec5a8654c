"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  cn,
} from "@/components/ui/select";
import { useDashboardAdminFilterContext } from "@/contexts/admin/dashboard-admin-filter";
import { useDashboardFilters } from "@/hooks/admin/dashboard/dashboard-filters.hook";
import { getStatusStringWithoutStatusId } from "@/lib/admin/dashboard/status-utils";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import React from "react";
import { IoReloadOutline } from "react-icons/io5";
import { STATUSES } from "./constants";

const Field: React.FC<{
  id: string;
  label: string;
  className?: string;
  children: React.ReactNode;
}> = ({ id, label, className, children }) => (
  <div className={`flex flex-col gap-2 ${className}`}>
    <label
      htmlFor={id}
      className="font-semibold md:text-base text-xs whitespace-nowrap overflow-hidden text-ellipsis"
    >
      {label}
    </label>
    {children}
  </div>
);

export const TrackingFilters: React.FC = () => {
  const {
    date,
    setDate,
    proposalNumber,
    setProposalNumber,
    status,
    setStatus,
    type,
    setType,
    itemsLimitPerPage,
    setItemsLimitPerPage,
    resetFilters,
  } = useDashboardAdminFilterContext();

  const {
    isCalendarDateOpen,
    setCalendarDateOpen,
    handleStatusChange,
    handleTypeChange,
    handleDateSelect,
  } = useDashboardFilters({ setStatus, setType, setDate });

  const capitalizeFirstLetter = (str: string) =>
    str ? str.charAt(0).toUpperCase() + str.slice(1) : str;

  const statusValue = status ? getStatusStringWithoutStatusId(status) : "Todos";
  const typeValue = type || "todos";

  return (
    <section className="mb-8 grid text-textColorPrimary grid-cols-3 gap-4 md:flex md:flex-wrap md:justify-center">
      <Field id="status-select" label="Status" className="order-1 md:w-[200px]">
        <Select
          name="status"
          value={statusValue}
          onValueChange={handleStatusChange}
        >
          <SelectTrigger
            id="status-select"
            className={cn(
              "w-full md:w-[200px] border border-PormadeGreen rounded-md px-4 py-2 shadow-sm transition-colors",
              !status && "text-muted-foreground",
            )}
          >
            {capitalizeFirstLetter(statusValue)}
          </SelectTrigger>
          <SelectContent className="bg-bgPrimaryWhite shadow-md rounded-md">
            <SelectItem value="Todos">Todos</SelectItem>
            {STATUSES.map((item) => (
              <SelectItem key={item.label} value={item.label}>
                {item.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </Field>
      <Field id="type-select" label="Tipo" className="order-2 md:w-[200px]">
        <Select name="type" value={typeValue} onValueChange={handleTypeChange}>
          <SelectTrigger
            id="type-select"
            className={cn(
              "w-full md:w-[200px] border border-PormadeGreen rounded-md px-4 py-2 shadow-sm transition-colors",
              !type && "text-muted-foreground",
            )}
          >
            {capitalizeFirstLetter(typeValue)}
          </SelectTrigger>
          <SelectContent className="bg-bgPrimaryWhite shadow-md rounded-md">
            <SelectItem value="todos">Todos</SelectItem>
            <SelectItem value="em-andamento">Em andamento</SelectItem>
            <SelectItem value="finalizado">Finalizado</SelectItem>
          </SelectContent>
        </Select>
      </Field>
      <Field
        id="date-picker"
        label="Data de atualização"
        className="order-3 md:order-4 md:w-[240px]"
      >
        <Popover open={isCalendarDateOpen} onOpenChange={setCalendarDateOpen}>
          <PopoverTrigger asChild>
            <Button
              id="date-picker"
              variant="outline"
              className={cn(
                "w-full md:w-[240px] border bg-bgPrimaryWhite border-PormadeGreen pl-3 text-left text-xs md:text-base font-normal",
                !date && "text-muted-foreground",
              )}
            >
              {date ? (
                format(date, "dd/MM/yyyy")
              ) : (
                <>
                  <span className="md:hidden">Selecione...</span>
                  <span className="hidden md:inline">Selecione a data</span>
                </>
              )}
              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0 border-none" align="start">
            <Calendar
              mode="single"
              selected={date || undefined}
              onSelect={handleDateSelect}
              disabled={(day: Date) =>
                day > new Date() || day < new Date("1900-01-01")
              }
            />
          </PopoverContent>
        </Popover>
      </Field>
      <div className="order-4 md:order-5 col-span-3 grid grid-cols-3 gap-4 md:flex md:justify-center">
        <Field
          id="proposalNumber"
          label="Número da Proposta"
          className="col-span-2 md:col-auto md:w-[200px] md:mr-4"
        >
          <Input
            id="proposalNumber"
            name="proposalNumber"
            value={proposalNumber || ""}
            onChange={(e) => setProposalNumber(e.target.value)}
            className="w-full md:w-[200px] border autocomplete-none border-PormadeGreen bg-bgPrimaryWhite rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-PormadeGreen shadow-sm"
            placeholder="Pesquisar por proposta..."
          />
        </Field>
        <Field
          id="itemsPerPage"
          label="Itens por Página"
          className="col-span-1 md:col-auto md:w-[200px]"
        >
          <Select
            name="itemsPerPage"
            value={itemsLimitPerPage.toString()}
            onValueChange={(value) => setItemsLimitPerPage(Number(value))}
          >
            <SelectTrigger
              id="itemsPerPage-select"
              className={cn(
                "w-full md:w-[200px] border border-PormadeGreen rounded-md px-4 py-2 shadow-sm transition-colors",
                !itemsLimitPerPage && "text-muted-foreground",
              )}
            >
              {itemsLimitPerPage}
            </SelectTrigger>
            <SelectContent className="bg-bgPrimaryWhite shadow-md rounded-md">
              {["50", "100", "200", "500"].map((item) => (
                <SelectItem key={item} value={item}>
                  {item}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </Field>
        <div className="flex flex-col gap-2 items-center justify-center col-span-3 md:col-auto md:w-[200px]">
          <label className="opacity-0 font-semibold md:text-base text-xs whitespace-nowrap overflow-hidden text-ellipsis">
            Resetar Filtros
          </label>
          <Button
            type="button"
            variant="outline"
            className="w-full md:w-[200px] bg-PormadeGreen text-white font-bold border-PormadeGreen border rounded-md px-4 py-2 shadow-sm transition-colors"
            onClick={resetFilters}
          >
            Resetar Filtros <IoReloadOutline />
          </Button>
        </div>
      </div>
    </section>
  );
};
