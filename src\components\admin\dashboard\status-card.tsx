import {
  ITrackingProposal,
  StatusStringType,
} from "@/services/admin/requests/tracking-find-all";
import { motion } from "framer-motion";
import React from "react";
import { STATUSES, getColorForStatus } from "./constants";

export interface StatusCardProps {
  status: StatusStringType;
  proposals: ITrackingProposal[];
  onSelectProposal: (proposal: ITrackingProposal) => void;
  isLoading: boolean;
}

const LoadingCard: React.FC = () => (
  <div className="p-4 rounded-lg h-[100px] animate-pulseLoading cursor-pointer transition-all duration-300" />
);

export const ProposalCard: React.FC<{
  proposal: ITrackingProposal;
  borderColor: string;
  onSelect: (proposal: ITrackingProposal) => void;
}> = ({ proposal, borderColor, onSelect }) => (
  <article
    key={proposal.proposal}
    onClick={() => onSelect(proposal)}
    style={{
      borderColor,
      backgroundColor: `${borderColor}33`,
    }}
    className="p-4 rounded-lg border cursor-pointer transition-all duration-300"
  >
    <h3 className="text-lg font-semibold text-textColorPrimary">
      Proposta: {proposal.proposal}
    </h3>
    <p className="text-sm text-gray-400">
      {proposal.finished ? "Finalizado" : "Em andamento"}
    </p>
  </article>
);

export const StatusCard: React.FC<StatusCardProps> = ({
  status,
  proposals,
  onSelectProposal,
  isLoading,
}) => {
  const statusConfig = STATUSES.find((s) => s.label === status);
  const Icon = statusConfig?.icon ?? (() => null);
  const borderColor = getColorForStatus(status);

  return (
    <>
      <motion.section
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        transition={{ duration: 0.3 }}
        style={{ borderColor: `${borderColor}99` }}
        className="md:min-w-[280px] min-w-full text-textColorPrimary border flex md:w-[250px] w-full flex-col justify-between p-4 bg-bgPrimaryWhite rounded-lg shadow-sm "
        aria-label={`Status card for ${status}`}
      >
        <header className="mb-4">
          <h2 className="text-xl font-semibold text-center mb-4 flex items-center justify-center gap-2">
            {React.createElement(Icon as React.FC<{ className?: string }>, {
              className: "w-6 h-6",
            })}
            {status}
          </h2>
        </header>

        <div className="flex-1 gap-3 flex flex-col">
          {isLoading ? (
            <LoadingCard />
          ) : proposals.length > 0 ? (
            proposals.map((proposal) => (
              <ProposalCard
                key={proposal.proposal}
                proposal={proposal}
                borderColor={borderColor}
                onSelect={onSelectProposal}
              />
            ))
          ) : (
            <p className="text-sm text-gray-400 text-center">
              Nenhuma proposta
            </p>
          )}
        </div>

        <footer className="mt-3 text-sm text-gray-500 text-center">
          {proposals.length} {proposals.length === 1 ? "proposta" : "propostas"}
        </footer>
      </motion.section>
    </>
  );
};
