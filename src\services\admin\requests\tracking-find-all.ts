"use server";

import { StatusEnum } from "@/components/admin/dashboard/constants";
import { getCookies } from "@/lib/cookies/saveCookies";
import { handleGlobalErrors } from "@/lib/handleErrors/handleRequestErros";
import { apiAdmin } from "@/services/api/apiInstance";
import { IApiInstance } from "@/types/utils";
import { TRACKING_ROUTES } from "../endpoints";

export interface ITrackingProposal {
  id: number;
  proposal: string;
  status: StatusStringType;
  finished: boolean;
  createdAt: string;
  updatedAt: string;
}

export type StatusStringType =
  | "Aprovado"
  | "Em produção"
  | "Manufaturado"
  | "Expedido"
  | "Em trânsito"
  | "Entregue";

// export async function generateTrackingProposals(
//   limit: number,
// ): Promise<ITrackingProposal[]> {
//   const statuses: StatusStringType[] = [
//     "Aprovado",
//     "Em produção",
//     "Manufaturado",
//     "Expedido",
//     "Em trânsito",
//     "Entregue",
//   ];

//   const proposals: ITrackingProposal[] = [];

//   for (let i = 0; i < limit; i++) {
//     const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
//     proposals.push({
//       id: i + 1,

//       proposal: (i + 1).toString().padStart(5, "0"),
//       status: randomStatus,
//       finished: false,
//       createdAt: new Date().toISOString(),
//       updatedAt: new Date().toISOString(),
//     });
//   }

//   return proposals;
// }
export const trackingFindAll = async ({
  limit,
  clientId,
  proposal,
  statusId,
  finished,
  lastUpdateDate,
}: {
  limit: number;
  clientId: string;
  proposal?: string;
  statusId?: StatusEnum;
  finished?: boolean;
  lastUpdateDate?: Date;
}): Promise<IApiInstance<ITrackingProposal[]>> => {
  // const data = await generateTrackingProposals(limit);
  // return { success: true, data: data, status: 201 };

  try {
    const token = await getCookies("tracker_admin_token");
    const { data, status } = await apiAdmin.get<ITrackingProposal[]>(
      TRACKING_ROUTES.FIND_ALL({
        limit,
        proposal,
        statusId,
        finished,
        lastUpdateDate,
      }),
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "X-Client-ID": clientId,
        },
      },
    );

    return { success: true, data, status: status };
  } catch (error) {
    return handleGlobalErrors(error);
  }
};
