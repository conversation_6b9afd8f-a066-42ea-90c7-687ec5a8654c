export const EventsLoading = () => {
  return (
    <>
      <div className="rounded-b-xl w-full bg-bgPrimaryWhite ">
        <div className="flex p-5 w-full overflow-hidden h-full flex-col  max-h-[450px] min-h-[450px]">
          <div className="flex  w-full h-[50px] sm:h-[40px] justify-center items-center">
            <div className="w-[40%] flex justify-start font-bold">
              <h1 className="text-xs w-[95%] h-8 animate-pulseLoading sm:text-base text-textColorPrimary">
                Informações da Entrega
              </h1>
            </div>
            <hr className="w-[60%] h-[1px] border-borderDetailsProposal" />
          </div>

          <div
            className="m-3 pb-6
           w-full h-full flex mt-20 gap-2 flex-col"
          >
            {/* <span className="w-full h-5 flex  animate-pulseLoading "></span>
              <span className="w-full h-5 flex  animate-pulseLoading "></span> */}

            {[...Array(5)].map((_, index) => (
              <div
                key={index}
                className="w-full h-5 flex  animate-pulseLoading "
              ></div>
            ))}
          </div>
        </div>
        <div className="text-xs min-h-[40px] items-center text-center text-textColorPrimary border-t  border-borderItemTableDelivery  bottom-0 absolute flex justify-center w-full">
          <p>
            OBS: As informações de entrega são de total responsabilidade da
            transportadora.
          </p>
        </div>
      </div>
    </>
  );
};
