"use client";
import { TrackingDashboard } from "@/components/admin/dashboard/tracking-dash";
import { HeaderAdmin } from "@/components/admin/header-admin";
import { DashboardAdminFilterProvider } from "@/contexts/admin/dashboard-admin-filter";
import { useLayoutEffect } from "react";
import "../../../components/header/styles.css";

const DashboardPage = () => {
  useLayoutEffect(() => {
    document.title = "Tracker admin";
  }, []);

  return (
    <main className="flex flex-col min-h-screen bg-PormadeGray">
      <DashboardAdminFilterProvider>
        <HeaderAdmin />
        <section className="flex-1 h-full flex min-h-0">
          <div className="flex-1">
            <TrackingDashboard />
          </div>
        </section>
      </DashboardAdminFilterProvider>
    </main>
  );
};

export default DashboardPage;
