"use client";

import { Theme } from "@/contexts/theme/themeContext";
import Cookies from "js-cookie";

const THEME_COOKIE_NAME = "user-theme";
const COOKIE_EXPIRATION_DAYS = 365;

export const getThemeCookie = (): Theme | null => {
  const theme = Cookies.get(THEME_COOKIE_NAME);
  return theme as Theme | null;
};

export const setThemeCookie = (theme: Theme): void => {
  Cookies.set(THEME_COOKIE_NAME, theme, {
    expires: COOKIE_EXPIRATION_DAYS,
    sameSite: "strict",
    secure: true,
  });
};

export const removeThemeCookie = (): void => {
  Cookies.remove(THEME_COOKIE_NAME);
};
