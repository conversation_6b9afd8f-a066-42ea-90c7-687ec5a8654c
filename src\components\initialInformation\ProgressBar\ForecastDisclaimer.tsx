"use client";
import React from "react";

interface ForecastDisclaimerProps {
  variant?: "desktop" | "mobile";
  className?: string;
}

const ForecastDisclaimer: React.FC<ForecastDisclaimerProps> = ({
  variant = "desktop",
  className = "",
}) => {
  const baseStyle = "text-[10px] leading-snug mt-1  ";
  const responsiveAdjust =
    variant === "mobile"
      ? " text-[11px] px-2 text-center"
      : " text-[11px] w-full text-end";

  return (
    <p
      className={`${baseStyle} !text-textColorPrimary/70 ${responsiveAdjust} ${className}`}
    >
      Esta data é uma <strong>estimativa de entrega</strong> baseada nos prazos
      de produção da fábrica e do transportador. Pode sofrer alterações devido a
      ajustes operacionais, imprevistos na produção, condições climáticas,
      feriados ou mudanças no agendamento de coleta/entrega. Continue
      acompanhando as atualizações para informações mais precisas.
    </p>
  );
};

export default ForecastDisclaimer;
