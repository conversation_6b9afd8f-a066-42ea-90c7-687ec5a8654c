"use client";
import TransportFromAnother from "@/components/transportFromAnother";
import TransportFromAnotherInTransit from "@/components/transportFromAnother/transporter-in-transit";
// import { useAcessContext } from "@/contexts/acess/acessContext";
import { useDataContext } from "@/contexts/data/dataContext";
import { useActiveDelivery } from "@/hooks/deliveriesDetails/useCurrentDeliveries";
import useInitialInformation from "@/hooks/inititalInformation/useInitialInformation";

const Destination = () => {
  const { endereco } = useInitialInformation();
  const { proposalData, deliveriesData } = useDataContext();
  // const { currentProductionSelected } = useAcessContext();
  const { getCurrentDelivery } = useActiveDelivery();

  // const getCurrentDelivery = deliveriesData?.find(
  //   (item) => item.order === currentProductionSelected,
  // );

  return (
    <div
      id="Shipping Destination"
      className="bg-bgPrimaryWhite flex-col border border-PormadeGreen  shadow-md rounded-lg py-6 px-3 sm:px-4 sm:py-6 md:p-6 flex justify-between lg:block"
    >
      <h1 className="text-textColorPrimary  text-base md:text-3xl font-bold flex-none">
        {proposalData ? (
          <> Local de {proposalData?.hasDelivery ? "Destino" : "Retirada"}</>
        ) : (
          "Carregando..."
        )}
      </h1>
      <div
        id="Status Time"
        className="text-textColorPrimary text-sm flex-none md:text-base"
      >
        {!proposalData ? (
          <DestinationLoading />
        ) : (
          <div className="flex mt-4 flex-col gap-1">
            <p>
              <strong>Logradouro: </strong>
              {` ${endereco?.rua}, ${endereco?.numero}`}
            </p>
            {endereco?.complemento &&
              endereco?.complemento.toLowerCase() !== "sem" && (
                <p>
                  <strong>Complemento: </strong>
                  {` ${endereco?.complemento}`}
                </p>
              )}
            <p>
              <strong>Bairro: </strong>
              {` ${endereco?.bairro}`}
            </p>
            <p>
              <strong>Cidade: </strong>
              {` ${endereco?.cidade} - ${endereco?.uf}`}
            </p>
            <p>
              <strong>CEP: </strong>
              {` ${endereco?.cep}`}
            </p>
          </div>
        )}
      </div>

      {getCurrentDelivery({
        delivery: deliveriesData,
        isNew: proposalData?.newProposal,
      })?.deliveryByPartner.isPartner && (
        <>
          <TransportFromAnother nrProposal={proposalData?.proposal ?? ""} />
          <TransportFromAnotherInTransit
            nrProposal={proposalData?.proposal ?? ""}
          />
        </>
      )}
    </div>
  );
};

const DestinationLoading = () => {
  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-col gap-2">
        <div
          id="Status Time"
          className="text-textColorPrimary text-xs flex-none sm:text-base animate-pulseLoading h-6"
        ></div>
        <div
          id="Status Time"
          className="text-textColorPrimary text-xs flex-none sm:text-base animate-pulseLoading h-6"
        ></div>
        <div
          id="Status Time"
          className="text-textColorPrimary text-xs flex-none sm:text-base animate-pulseLoading h-6"
        ></div>
        <div
          id="Status Time"
          className="text-textColorPrimary text-xs flex-none sm:text-base animate-pulseLoading h-6"
        ></div>
      </div>
    </div>
  );
};

export default Destination;
