"use client";

import { Logo } from "@/components/header/logo";
import { HeaderTheme } from "@/components/header/theme";
import { useDashboardAdminFilterContext } from "@/contexts/admin/dashboard-admin-filter";
import { useTheme } from "@/contexts/theme/themeContext";
import { useLogoutAdminMutation } from "@/hooks/admin/login-adm/logout.mutation.hook";
import { getUser } from "@/lib/admin/user/get-user";
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useState } from "react";
import { FiMenu, FiX } from "react-icons/fi";
import { LuMoon, LuSun } from "react-icons/lu";
import { VscSignOut } from "react-icons/vsc";
import "../../header/styles.css";

export const HeaderAdmin = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { toggleTheme, theme } = useTheme();
  const [isTimeoutActive, setIsTimeoutActive] = useState(false);
  const { logout } = useLogoutAdminMutation();
  const { setUsername, username } = useDashboardAdminFilterContext();

  const handleClickTheme = () => {
    if (!isTimeoutActive) {
      setIsTimeoutActive(true);
      toggleTheme();
      setTimeout(() => {
        setIsTimeoutActive(false);
      }, 500);
    }
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen((prev) => !prev);
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, transition: { duration: 0.2 } },
  };

  const mobileMenuVariants = {
    hidden: { x: "100%", opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 30 },
    },
    exit: {
      x: "100%",
      opacity: 0,
      transition: { type: "spring", stiffness: 300, damping: 30 },
    },
  };

  useEffect(() => {
    getUser().then((token: string | null) => {
      if (token) {
        setUsername(token);
      }
    });
  }, [setUsername]);

  return (
    <header
      role="banner"
      className="bg-bgPrimaryWhite w-full flex items-center justify-center py-7 shadow-3xl relative"
    >
      <div className="my-auto w-full">
        <div className="flex justify-between items-center mx-2">
          <div className="ml-2">
            <Logo />
          </div>
          <nav
            className="mr-[50px] h-full hidden flex-row sm:flex"
            aria-label="Admin actions"
          >
            <section
              className="w-[120px] mr-5  text-end flex flex-col"
              aria-label="User greeting"
            >
              <h2 className="text-textColorPrimary">
                Olá, {username?.split(" ")[0]}!
              </h2>
              <span className="text-PormadeGreen">Administrador</span>
            </section>
            <ul className="flex  justify-center items-center space-x-2">
              <li className="group relative flex items-center">
                <span
                  className="absolute left-1/4 transform -translate-x-1/2 bottom-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 
                 text-textColorPrimary text-sm px-2 py-1 rounded whitespace-nowra"
                >
                  Tema
                </span>
                <HeaderTheme />
              </li>
              <li className="flex items-center">
                <button
                  className="group relative flex justify-between w-full items-center text-white cursor-pointer"
                  onClick={() => logout()}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === "Space") {
                      logout();
                    }
                  }}
                >
                  <VscSignOut className="text-textColorPrimary text-3xl" />
                  <span
                    className="absolute left-1/2 transform -translate-x-1/2 bottom-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 
                 text-textColorPrimary text-sm px-2 py-1 rounded whitespace-nowrap"
                  >
                    Sair
                  </span>
                </button>
              </li>
            </ul>
          </nav>
          <div className="mr-4 sm:hidden">
            <button
              type="button"
              onClick={toggleMobileMenu}
              aria-label={mobileMenuOpen ? "Fechar menu" : "Abrir menu"}
              className="text-textColorPrimary text-3xl"
            >
              {mobileMenuOpen ? <FiX /> : <FiMenu />}
            </button>
          </div>
          <AnimatePresence>
            {mobileMenuOpen && (
              <motion.div
                className="fixed top-0 left-0 w-full h-full bg-bgPrimaryWhite/20 backdrop-blur-sm z-40"
                variants={backdropVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                onClick={() => setMobileMenuOpen(false)}
              >
                <motion.nav
                  onClick={(e) => e.stopPropagation()}
                  className="fixed top-0 right-0 w-2/4 h-full bg-bgPrimaryWhite  rounded-s-lg border-l border-PormadeGreen shadow-3xl z-50 p-4"
                  aria-label="Menu mobile"
                  variants={mobileMenuVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                >
                  <div className="h-1/10 flex justify-end">
                    <button
                      type="button"
                      aria-label="Fechar menu"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <FiX className="text-textColorPrimary text-3xl" />
                    </button>
                  </div>
                  <section
                    className="mb-8 text-center"
                    aria-label="User greeting"
                  >
                    <h2 className="text-textColorPrimary text-lg">
                      Olá, {username?.split(" ")[0]}!
                    </h2>
                    <span className="text-PormadeGreen">Administrador</span>
                  </section>
                  <ul className="space-y-4 justify-center items-center">
                    <li className="w-full">
                      <button
                        type="button"
                        aria-label="Sair"
                        onClick={handleClickTheme}
                        className="flex items-center  border border-gray-200/30 hover:bg-gray-40 p-4 rounded-md text-textColorPrimary justify-center w-full"
                      >
                        <div className="flex items-center justify-center mr-8 text-3xl disabled:opacity-30 theme-button">
                          {theme === "light" || !theme ? (
                            <LuMoon className="sunIcon" />
                          ) : (
                            <LuSun className="sunIcon" />
                          )}
                        </div>
                        <span className="ml-2">Tema</span>
                      </button>
                    </li>
                    <li>
                      <button
                        type="button"
                        aria-label="Sair"
                        className="flex items-center  border border-gray-200/30 hover:bg-gray-40 p-4 rounded-md text-textColorPrimary justify-center w-full"
                      >
                        <div className="flex items-center justify-center mr-8 text-3xl disabled:opacity-30 theme-button">
                          <VscSignOut className="sunIcon" />
                        </div>
                        <span className="ml-2">Sair</span>
                      </button>
                    </li>
                  </ul>
                </motion.nav>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </header>
  );
};
