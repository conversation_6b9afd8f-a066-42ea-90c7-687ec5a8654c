export const Dobradica = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="198"
    height="257"
    viewBox="0 0 198 257"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="1"
      y="11"
      width="196"
      height="228"
      rx="49"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="78.5"
      y="9.5"
      width="37"
      height="230"
      fill="white"
      stroke="white"
    />
    <circle
      cx="173.5"
      cy="124.5"
      r="16.5"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <circle
      cx="173.5"
      cy="124.5"
      r="9.5"
      fill="white"
      stroke="black"
      strokeWidth="2"
    />
    <circle
      cx="24.5"
      cy="124.5"
      r="16.5"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <circle
      cx="24.5"
      cy="124.5"
      r="9.5"
      fill="white"
      stroke="black"
      strokeWidth="2"
    />
    <circle
      cx="32"
      cy="206"
      r="13"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <circle
      cx="32.0001"
      cy="206"
      r="7.4"
      fill="white"
      stroke="black"
      strokeWidth="2"
    />
    <circle
      cx="170"
      cy="206"
      r="13"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <circle
      cx="170"
      cy="206"
      r="7.4"
      fill="white"
      stroke="black"
      strokeWidth="2"
    />
    <circle
      cx="166"
      cy="39"
      r="13"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <circle
      cx="166"
      cy="39.0001"
      r="7.4"
      fill="white"
      stroke="black"
      strokeWidth="2"
    />
    <circle
      cx="32"
      cy="39"
      r="13"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <circle
      cx="32.0001"
      cy="39.0001"
      r="7.4"
      fill="white"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M80 187C80 185.895 80.8954 185 82 185H115V238H82C80.8954 238 80 237.105 80 236V187Z"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M114.039 149.001C114.04 150.105 113.145 151.001 112.04 151.002L79.0403 151.027L79.0006 98.027L112.001 98.0023C113.105 98.0014 114.001 98.8962 114.002 100.001L114.039 149.001Z"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M80.3855 14.0073C80.3936 12.9028 81.2957 12.014 82.4002 12.0222L115.399 12.2667L115.007 65.2653L82.0075 65.0207C80.903 65.0126 80.0142 64.1105 80.0224 63.006L80.3855 14.0073Z"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="79.5"
      y="67.5"
      width="35"
      height="28"
      rx="2.5"
      fill="black"
      stroke="black"
    />
    <rect
      x="79.5"
      y="153.5"
      width="35"
      height="29"
      rx="2.5"
      fill="black"
      stroke="black"
    />
    <g filter="url(#filter0_d_605_240)">
      <rect x="79" y="240" width="36" height="9" rx="3" fill="black" />
      <rect x="79.5" y="240.5" width="35" height="8" rx="2.5" stroke="black" />
    </g>
    <g filter="url(#filter1_d_605_240)">
      <rect x="79" width="36" height="9" rx="3" fill="black" />
      <rect x="79.5" y="0.5" width="35" height="8" rx="2.5" stroke="black" />
    </g>
    <defs>
      <filter
        id="filter0_d_605_240"
        x="75"
        y="240"
        width="44"
        height="17"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_605_240"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_605_240"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_d_605_240"
        x="75"
        y="0"
        width="44"
        height="17"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_605_240"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_605_240"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
