export const PortaTrilhoAparente = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="534"
    height="663"
    viewBox="0 0 534 663"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="163.5"
      y="47.5"
      width="289"
      height="615"
      fill="#D9D9D9"
      stroke="black"
    />
    <g filter="url(#filter0_d_754_150)">
      <path d="M409 266H421V445H409V266Z" fill="#010101" />
      <path d="M409 266H421V445H409V266Z" stroke="black" strokeWidth="2" />
    </g>
    <g filter="url(#filter1_i_754_150)">
      <rect y="16" width="534" height="20" fill="black" />
      <rect x="197" y="16" width="14" height="48" fill="black" />
      <circle cx="204" cy="10" r="10" fill="black" />
      <rect x="407" y="16" width="14" height="48" fill="black" />
      <circle cx="414" cy="10" r="10" fill="black" />
    </g>
    <defs>
      <filter
        id="filter0_d_754_150"
        x="408"
        y="265"
        width="26"
        height="189"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="8" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_754_150"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_754_150"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_i_754_150"
        x="0"
        y="0"
        width="534"
        height="68"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_754_150"
        />
      </filter>
    </defs>
  </svg>
);
