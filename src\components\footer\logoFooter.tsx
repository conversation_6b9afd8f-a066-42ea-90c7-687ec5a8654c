"use client";
import Image from "next/image";
import Pormade<PERSON>ogo from "../../assets/images/logo_preta.png";
import PormadeLogoBranca from "../../assets/images/logo_branco.png";
import { useTheme } from "@/contexts/theme/themeContext";

export const Logo = () => {
    const { theme } = useTheme();

    return (
        <Image
            src={theme === "light" ? PormadeLogo : PormadeLogoBranca}
            alt="Logo Pormade"
            className="h-14 w-auto"
            priority={true}
            style={{ objectFit: "contain" }}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
    );
};
