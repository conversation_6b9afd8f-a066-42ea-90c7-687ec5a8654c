
.theme-button {
    transition: transform 0.3s ease-in-out, color 0.3s ease-in-out;
  }
  
  .theme-button:hover {
    transform: scale(1.1);
  }
  
  .theme-button.sun:hover {
    color: rgb(255, 255, 255);
  }
  
  .theme-button.moon:hover {
    color: #000000; 
  }
  
  .theme-button.sun {
    color: var(--text-color-primary);
  }
  
  .theme-button.moon {
    color: #000000;
  }
  

  .moonIcon {
    transition: cubic-bezier(0.075, 0.82, 0.165, 1);
  }

  .moonIcon:hover {
    transform: scale(1.1);
  }

  .sunIcon {
    transition: cubic-bezier(0.075, 0.82, 0.165, 1);
  }

  .sunIcon:hover {
    transform: scale(1.1);
  }
  

  